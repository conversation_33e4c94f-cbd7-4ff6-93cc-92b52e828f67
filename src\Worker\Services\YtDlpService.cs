using System.Text;
using System.Text.Json;
using CliWrap;
using Shared.Common;
using Shared.DTOs;

namespace Worker.Services;

public class YtDlpService
{
    private const int DefaultTimeoutSeconds = 60;
    private const int DownloadTimeoutMinutes = 10;
    private const int HealthCheckTimeoutSeconds = 5;

    private readonly IConfiguration _configuration;
    private readonly ILogger<YtDlpService> _logger;
    private readonly string _ytDlpPath;

    public YtDlpService(ILogger<YtDlpService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _ytDlpPath = _configuration["Worker:YtDlpPath"] ?? throw new InvalidOperationException("yt-dlp路径未配置");
    }

    public async Task<ServiceResult<YouTubeVideoResponse>> FetchVideoAsync(string videoId, ProxyInfo? proxy = null)
    {
        return await ExecuteYtDlpAsync<YouTubeVideoResponse>($"https://www.youtube.com/watch?v={videoId}", ["--dump-json"], proxy);
    }

    public async Task<ServiceResult<YouTubePlaylistResponse>> FetchPlaylistAsync(string playlistId, ProxyInfo? proxy = null, int? maxVideos = null)
    {
        return await FetchPlaylistOrChannelAsync<YouTubePlaylistResponse>($"https://www.youtube.com/playlist?list={playlistId}", proxy, maxVideos);
    }

    public async Task<ServiceResult<YouTubeChannelResponse>> FetchChannelAsync(string channelId, ProxyInfo? proxy = null, int? maxVideos = null)
    {
        return await FetchPlaylistOrChannelAsync<YouTubeChannelResponse>($"https://www.youtube.com/channel/{channelId}/videos", proxy, maxVideos);
    }

    private async Task<ServiceResult<T>> FetchPlaylistOrChannelAsync<T>(string url, ProxyInfo? proxy = null, int? maxVideos = null)
    {
        var args = new List<string> { "--dump-json", "--flat-playlist" };
        if (maxVideos.HasValue) args.AddRange(["--playlist-end", maxVideos.Value.ToString()]);
        return await ExecuteYtDlpAsync<T>(url, args, proxy);
    }

    public async Task<ServiceResult<T>> ExecuteYtDlpAsync<T>(string url, List<string> args, ProxyInfo? proxy = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var commandArgs = new List<string>(args);

            if (proxy != null)
            {
                var proxyUrl = FormatProxyUrl(proxy);
                commandArgs.AddRange(["--proxy", proxyUrl]);
            }

            var additionalArgs = _configuration["Worker:YtDlpAdditionalArgs"];
            if (!string.IsNullOrEmpty(additionalArgs))
                commandArgs.AddRange(additionalArgs.Split(' ', StringSplitOptions.RemoveEmptyEntries));

            commandArgs.Add(url);

            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(DefaultTimeoutSeconds));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, cts.Token);

            var stdOutBuffer = new StringBuilder();
            var stdErrBuffer = new StringBuilder();

            var result = await Cli.Wrap(_ytDlpPath).WithArguments(commandArgs).WithStandardOutputPipe(PipeTarget.ToStringBuilder(stdOutBuffer))
                .WithStandardErrorPipe(PipeTarget.ToStringBuilder(stdErrBuffer)).WithValidation(CommandResultValidation.None).ExecuteAsync(combinedCts.Token);

            if (result.ExitCode != 0)
            {
                var errorOutput = stdErrBuffer.ToString();
                _logger.LogError("yt-dlp执行失败，退出代�? {ExitCode}，错�? {Error}", result.ExitCode, errorOutput);
                return ServiceResult<T>.Failure($"yt-dlp执行失败: {errorOutput}");
            }

            var jsonOutput = stdOutBuffer.ToString().Trim();
            if (string.IsNullOrEmpty(jsonOutput))
                return ServiceResult<T>.Failure("yt-dlp返回空输出");

            try
            {
                var data = JsonSerializer.Deserialize<T>(jsonOutput, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                return data != null ? ServiceResult<T>.Success(data) : ServiceResult<T>.Failure("JSON反序列化返回null");
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "解析yt-dlp JSON输出失败: {Output}", jsonOutput);
                return ServiceResult<T>.Failure($"JSON解析失败: {ex.Message}");
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("yt-dlp执行超时或被取消");
            return ServiceResult<T>.Failure("操作超时或被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行yt-dlp时发生意外错误");
            return ServiceResult<T>.Failure($"执行失败: {ex.Message}");
        }
    }

    private static string FormatProxyUrl(ProxyInfo proxy)
    {
        return $"{proxy.Type.ToLower()}://{proxy.Username}:{proxy.Password}@{proxy.Host}:{proxy.Port}";
    }

    private static string BuildYouTubeUrl(string videoId)
    {
        return $"https://www.youtube.com/watch?v={videoId}";
    }

    private static void AddTimeSliceArgs(List<string> args, int? startTime, int? endTime)
    {
        if (startTime.HasValue)
            args.AddRange(["--external-downloader-args", $"ffmpeg:-ss {startTime.Value}"]);
        if (endTime.HasValue && startTime.HasValue)
            args.AddRange(["--external-downloader-args", $"ffmpeg:-t {endTime.Value - startTime.Value}"]);
    }

    public async Task<ServiceResult<DownloadResult>> DownloadVideoAsync(VideoDownloadOptions options, CancellationToken cancellationToken = default)
    {
        var args = BuildVideoDownloadArgs(options);
        var duration = options.EndTime.HasValue && options.StartTime.HasValue
            ? TimeSpan.FromSeconds(options.EndTime.Value - options.StartTime.Value)
            : (TimeSpan?)null;
        return await ExecuteDownloadAsync(args, options.OutputPath, options.VideoId, "视频", options.Format ?? "mp4",
            f => !f.EndsWith(".part") && !f.EndsWith(".tmp"), duration, cancellationToken);
    }

    public async Task<ServiceResult<DownloadResult>> DownloadAudioAsync(AudioDownloadOptions options, CancellationToken cancellationToken = default)
    {
        var args = BuildAudioDownloadArgs(options);
        var duration = options.EndTime.HasValue && options.StartTime.HasValue
            ? TimeSpan.FromSeconds(options.EndTime.Value - options.StartTime.Value)
            : (TimeSpan?)null;
        return await ExecuteDownloadAsync(args, options.OutputPath, options.VideoId, "音频", options.Format ?? "mp3",
            f => !f.EndsWith(".part") && !f.EndsWith(".tmp"), duration, cancellationToken);
    }

    private async Task<ServiceResult<DownloadResult>> ExecuteDownloadAsync(List<string> args, string outputPath, string videoId, string downloadType,
        string defaultFormat, Func<string, bool> fileFilter, TimeSpan? duration = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await ExecuteDownloadCommandAsync(args, outputPath, cancellationToken);
            if (!result.IsSuccess) return ServiceResult<DownloadResult>.Failure(result.ErrorMessage!);

            var downloadedFile = Directory.GetFiles(outputPath, "*", SearchOption.TopDirectoryOnly).Where(fileFilter)
                .OrderByDescending(f => new FileInfo(f).CreationTime).FirstOrDefault();

            if (downloadedFile == null)
                return ServiceResult<DownloadResult>.Failure($"未找到下载的{downloadType}文件");
            var fileInfo = new FileInfo(downloadedFile);
            var downloadResult = new DownloadResult(downloadedFile, fileInfo.Length, defaultFormat, duration);

            return ServiceResult<DownloadResult>.Success(downloadResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载{DownloadType}时发生错�? {VideoId}", downloadType, videoId);
            return ServiceResult<DownloadResult>.Failure($"下载{downloadType}失败: {ex.Message}");
        }
    }

    public async Task<ServiceResult<DownloadResult>> DownloadThumbnailAsync(ThumbnailDownloadOptions options, CancellationToken cancellationToken = default)
    {
        return await ExecuteDownloadAsync(BuildThumbnailDownloadArgs(options), options.OutputPath, options.VideoId, "缩略�?, options.Format ?? "jpg",
        f => f.EndsWith(".jpg") || f.EndsWith(".png") || f.EndsWith(".webp"), null, cancellationToken);
    }

    public async Task<ServiceResult<DownloadResult>> DownloadSubtitleAsync(SubtitleDownloadOptions options, CancellationToken cancellationToken = default)
    {
        return await ExecuteDownloadAsync(BuildSubtitleDownloadArgs(options), options.OutputPath, options.VideoId, "字幕", options.Format ?? "srt",
            f => f.EndsWith(".srt") || f.EndsWith(".vtt") || f.EndsWith(".ass") || f.EndsWith(".lrc"), null, cancellationToken);
    }

    public async Task<ServiceResult<DownloadResult>> DownloadCommentsAsync(CommentDownloadOptions options, CancellationToken cancellationToken = default)
    {
        try
        {
            var args = BuildCommentDownloadArgs(options);
            var result = await ExecuteDownloadCommandAsync(args, options.OutputPath, cancellationToken);
            if (!result.IsSuccess) return ServiceResult<DownloadResult>.Failure(result.ErrorMessage!);

            var commentFile = Directory.GetFiles(options.OutputPath, "*.info.json", SearchOption.TopDirectoryOnly)
                .OrderByDescending(f => new FileInfo(f).CreationTime).FirstOrDefault();

            if (commentFile == null)
                return ServiceResult<DownloadResult>.Failure("未找到下载的评论文件");
            var outputFormat = options.Format?.ToLower() ?? "json";
            var finalFile = await ConvertCommentsAsync(commentFile, outputFormat);

            try
            {
                File.Delete(commentFile);
            }
            catch
            {
            }

            var fileInfo = new FileInfo(finalFile);
            var downloadResult = new DownloadResult(finalFile, fileInfo.Length, outputFormat);

            return ServiceResult<DownloadResult>.Success(downloadResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载评论时发生错�? {VideoId}", options.VideoId);
            return ServiceResult<DownloadResult>.Failure($"下载评论失败: {ex.Message}");
        }
    }

    public async Task<ServiceResult<DownloadResult>> DownloadDescriptionAsync(DescriptionDownloadOptions options, CancellationToken cancellationToken = default)
    {
        try
        {
            var videoInfo = await FetchVideoAsync(options.VideoId, options.Proxy);
            if (!videoInfo.IsSuccess)
                return ServiceResult<DownloadResult>.Failure($"获取视频信息失败: {videoInfo.ErrorMessage}");

            var description = videoInfo.Data?.Description ?? "";
            var title = videoInfo.Data?.Title ?? options.VideoId;
            var cleanTitle = CleanFileName(title);
            var descriptionFile = Path.Combine(options.OutputPath, $"{cleanTitle}.description.txt");

            await File.WriteAllTextAsync(descriptionFile, description, Encoding.UTF8, cancellationToken);

            var fileInfo = new FileInfo(descriptionFile);
            var downloadResult = new DownloadResult(descriptionFile, fileInfo.Length, "txt");

            return ServiceResult<DownloadResult>.Success(downloadResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载描述时发生错�? {VideoId}", options.VideoId);
            return ServiceResult<DownloadResult>.Failure($"下载描述失败: {ex.Message}");
        }
    }

    private async Task<ServiceResult<bool>> ExecuteDownloadCommandAsync(List<string> args, string outputPath, CancellationToken cancellationToken)
    {
        try
        {
            var commandArgs = new List<string>();
            commandArgs.AddRange(["--output", Path.Combine(outputPath, "%(title)s.%(ext)s")]);
            commandArgs.AddRange(["--no-playlist", "--no-warnings"]);

            if (_configuration.GetValue<bool>("Worker:YtDlpVerbose"))
                commandArgs.Add("--verbose");

            commandArgs.AddRange(args);

            using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(DownloadTimeoutMinutes));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, cts.Token);

            var result = await Cli.Wrap(_ytDlpPath).WithArguments(commandArgs).WithValidation(CommandResultValidation.None).ExecuteAsync(combinedCts.Token);

            return result.ExitCode == 0 ? ServiceResult<bool>.Success(true) : ServiceResult<bool>.Failure($"yt-dlp执行失败，退出代�? {result.ExitCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行yt-dlp下载命令时发生错�?);
            return ServiceResult<bool>.Failure($"执行下载命令失败: {ex.Message}");
        }
    }

    private static List<string> BuildVideoDownloadArgs(VideoDownloadOptions options)
    {
        var args = new List<string>();

        var format = options.Format?.ToLower() ?? "mp4";
        args.AddRange([
            "--format", format switch
            {
                "mp4" => "best[ext=mp4]/best",
                "webm" => "best[ext=webm]/best",
                "mkv" => "best[ext=mkv]/best",
                _ => "best"
            }
        ]);

        var quality = options.Quality?.ToLower();
        if (!string.IsNullOrEmpty(quality))
            args.AddRange([
                "--format", quality switch
                {
                    "highest" or "best" => "best",
                    "lowest" or "worst" => "worst",
                    _ when quality.EndsWith('p') => $"best[height<={quality[..^1]}]",
                    _ => "best"
                }
            ]);

        AddTimeSliceArgs(args, options.StartTime, options.EndTime);
        args.Add(BuildYouTubeUrl(options.VideoId));
        return args;
    }

    private static List<string> BuildAudioDownloadArgs(AudioDownloadOptions options)
    {
        var args = new List<string> { "--extract-audio" };

        var format = options.Format?.ToLower() ?? "mp3";
        args.AddRange([
            "--audio-format", format switch
            {
                "mp3" or "aac" or "flac" or "m4a" or "opus" or "vorbis" or "wav" => format,
                _ => "mp3"
            }
        ]);

        var quality = options.Quality?.ToLower();
        if (!string.IsNullOrEmpty(quality))
            args.AddRange([
                "--audio-quality", quality switch
                {
                    "highest" or "best" => "0",
                    "high" => "2",
                    "medium" => "5",
                    "low" => "7",
                    "lowest" or "worst" => "9",
                    _ => "5"
                }
            ]);

        AddTimeSliceArgs(args, options.StartTime, options.EndTime);
        args.Add(BuildYouTubeUrl(options.VideoId));
        return args;
    }

    private static List<string> BuildThumbnailDownloadArgs(ThumbnailDownloadOptions options)
    {
        var args = new List<string> { "--write-thumbnail", "--skip-download" };
        if (!string.IsNullOrEmpty(options.Format))
            args.AddRange(["--convert-thumbnails", options.Format]);
        args.Add(BuildYouTubeUrl(options.VideoId));
        return args;
    }

    private static List<string> BuildSubtitleDownloadArgs(SubtitleDownloadOptions options)
    {
        var args = new List<string> { "--write-subs", "--skip-download" };
        if (!string.IsNullOrEmpty(options.Language))
            args.AddRange(["--sub-langs", options.Language]);
        if (!string.IsNullOrEmpty(options.Format))
            args.AddRange(["--sub-format", options.Format]);
        if (options.AutoGenerated)
            args.Add("--write-auto-subs");
        args.Add(BuildYouTubeUrl(options.VideoId));
        return args;
    }

    private static List<string> BuildCommentDownloadArgs(CommentDownloadOptions options)
    {
        var commentLimit = Math.Min(options.MaxComments, 1000);
        return new List<string>
        {
            "--write-comments", "--write-info-json", "--skip-download",
            "--extractor-args", $"youtube:max_comments={commentLimit}",
            "--extractor-args", $"youtube:comment_sort={options.SortBy}",
            BuildYouTubeUrl(options.VideoId)
        };
    }

    private static async Task<string> ConvertCommentsAsync(string jsonFile, string outputFormat)
    {
        var outputFile = Path.ChangeExtension(jsonFile, $".comments.{outputFormat}");

        if (outputFormat == "json")
        {
            File.Copy(jsonFile, outputFile, true);
            return outputFile;
        }

        if (outputFormat == "csv")
        {
            var jsonContent = await File.ReadAllTextAsync(jsonFile);
            var videoInfo = JsonSerializer.Deserialize<JsonElement>(jsonContent);
            return await ConvertCommentsToCsvAsync(videoInfo, outputFile);
        }

        return outputFile;
    }

    private static async Task<string> ConvertCommentsToCsvAsync(JsonElement videoInfo, string outputFile)
    {
        using var writer = new StreamWriter(outputFile, false, Encoding.UTF8);
        await writer.WriteLineAsync("Author,Text,Timestamp,Likes");

        if (videoInfo.TryGetProperty("comments", out var comments) && comments.ValueKind == JsonValueKind.Array)
            foreach (var comment in comments.EnumerateArray())
            {
                var author = comment.TryGetProperty("author", out var a) ? EscapeCsv(a.GetString() ?? "") : "";
                var text = comment.TryGetProperty("text", out var t) ? EscapeCsv(t.GetString() ?? "") : "";
                var timestamp = comment.TryGetProperty("timestamp", out var ts) ? ts.GetString() ?? "" : "";
                var likes = comment.TryGetProperty("like_count", out var lc) ? lc.GetInt32().ToString() : "0";

                await writer.WriteLineAsync($"{author},{text},{timestamp},{likes}");
            }

        return outputFile;
    }

    private static string EscapeCsv(string value)
    {
        return $"\"{value.Replace("\"", "\"\"")}\"";
    }

    private static string CleanFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var cleanName = string.Concat(fileName.Where(c => !invalidChars.Contains(c)));
        return cleanName.Length > 200 ? cleanName[..200].Trim() : cleanName.Trim();
    }

    public async Task<bool> IsYtDlpAvailableAsync()
    {
        try
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(HealthCheckTimeoutSeconds));
            var result = await Cli.Wrap(_ytDlpPath).WithArguments("--version").WithValidation(CommandResultValidation.None).ExecuteAsync(cts.Token);
            return result.ExitCode == 0;
        }
        catch
        {
            return false;
        }
    }
}
namespace Worker.Scheduled;

public class FileCleanupService : BackgroundService
{
    private readonly HashSet<string> _activeDownloads = new();
    private readonly object _activeDownloadsLock = new();
    private readonly TimeSpan _cleanupInterval;
    private readonly IConfiguration _configuration;
    private readonly string _downloadPath;
    private readonly TimeSpan _errorRetryInterval;
    private readonly ILogger<FileCleanupService> _logger;
    private readonly TimeSpan _startupDelay;

    public FileCleanupService(ILogger<FileCleanupService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;

        _cleanupInterval = TimeSpan.FromHours(_configuration.GetValue("FileCleanup:CleanupInterval", 6));
        _startupDelay = TimeSpan.FromMinutes(_configuration.GetValue("FileCleanup:StartupDelay", 5));
        _errorRetryInterval = TimeSpan.FromMinutes(_configuration.GetValue("FileCleanup:ErrorRetryInterval", 60));
        _downloadPath = InitializeDownloadPath();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("文件清理服务已启动，间隔: {Interval} 小时，启动延迟: {Delay} 分钟", _cleanupInterval.TotalHours, _startupDelay.TotalMinutes);

        await Task.Delay(_startupDelay, stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
            try
            {
                await PerformFileCleanupAsync();
                await Task.Delay(_cleanupInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文件清理循环中发生错误");
                await Task.Delay(_errorRetryInterval, stoppingToken);
            }

        _logger.LogInformation("文件清理服务已停止");
    }

    public void MarkFileAsDownloading(string filePath)
    {
        lock (_activeDownloadsLock)
        {
            _activeDownloads.Add(filePath);
        }
    }

    public void MarkFileAsDownloadComplete(string filePath)
    {
        lock (_activeDownloadsLock)
        {
            _activeDownloads.Remove(filePath);
        }
    }

    private bool IsFileBeingDownloaded(string filePath)
    {
        lock (_activeDownloadsLock)
        {
            return _activeDownloads.Contains(filePath) || _activeDownloads.Any(active => filePath.StartsWith(Path.GetDirectoryName(active) ?? ""));
        }
    }

    private async Task PerformFileCleanupAsync()
    {
        try
        {
            if (!Directory.Exists(_downloadPath)) return;

            var files = Directory.GetFiles(_downloadPath, "*", SearchOption.AllDirectories);
            var fileExpirationHours = _configuration.GetValue("Worker:FileExpirationHours", 24);
            var cutoffTime = DateTime.UtcNow.AddHours(-fileExpirationHours);
            var cleanedCount = 0;

            _logger.LogDebug("开始文件清理，检查 {FileCount} 个文件", files.Length);

            foreach (var file in files)
                try
                {
                    if (IsFileBeingDownloaded(file))
                    {
                        _logger.LogDebug("跳过正在下载的文件: {FilePath}", file);
                        continue;
                    }

                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTimeUtc < cutoffTime)
                    {
                        if (IsFileLocked(file))
                        {
                            _logger.LogDebug("跳过被锁定的文件: {FilePath}", file);
                            continue;
                        }

                        File.Delete(file);
                        cleanedCount++;
                        _logger.LogDebug("删除过期文件: {FilePath}", file);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "删除过期文件失败: {FilePath}", file);
                }

            if (cleanedCount > 0)
                _logger.LogInformation("从 {Path} 清理了 {Count} 个过期文件", _downloadPath, cleanedCount);

            await CleanupEmptyDirectoriesAsync(_downloadPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件清理过程中发生错误");
            throw;
        }
    }

    private static bool IsFileLocked(string filePath)
    {
        try
        {
            using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.None);
            return false;
        }
        catch (IOException)
        {
            return true;
        }
        catch
        {
            return false;
        }
    }

    private async Task CleanupEmptyDirectoriesAsync(string basePath)
    {
        try
        {
            var directories = Directory.GetDirectories(basePath);
            foreach (var directory in directories)
            {
                await CleanupEmptyDirectoriesAsync(directory);

                if (!Directory.EnumerateFileSystemEntries(directory).Any())
                    try
                    {
                        Directory.Delete(directory);
                        _logger.LogDebug("删除空目录: {DirectoryPath}", directory);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "删除空目录失败: {DirectoryPath}", directory);
                    }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "清理空目录时发生错误: {BasePath}", basePath);
        }
    }

    private string InitializeDownloadPath()
    {
        try
        {
            var configuredPath = _configuration["Worker:DownloadPath"];
            string downloadPath;

            if (!string.IsNullOrEmpty(configuredPath))
            {
                downloadPath = Path.GetFullPath(configuredPath);
                _logger.LogInformation("使用配置的下载路径: {DownloadPath}", downloadPath);
            }
            else
            {
                downloadPath = Path.Combine(Directory.GetCurrentDirectory(), "downloads");
                _logger.LogInformation("使用默认下载路径: {DownloadPath}", downloadPath);
            }

            if (!Directory.Exists(downloadPath))
            {
                Directory.CreateDirectory(downloadPath);
                _logger.LogInformation("创建下载目录: {DownloadPath}", downloadPath);
            }

            var testFile = Path.Combine(downloadPath, $"test_{Guid.NewGuid()}.tmp");
            try
            {
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
                _logger.LogDebug("下载目录权限检查通过: {DownloadPath}", downloadPath);
            }
            catch (UnauthorizedAccessException)
            {
                _logger.LogError("下载目录没有写入权限: {DownloadPath}", downloadPath);
                throw new InvalidOperationException($"下载目录没有写入权限: {downloadPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载目录权限检查失败: {DownloadPath}", downloadPath);
                throw new InvalidOperationException($"下载目录权限检查失败: {downloadPath}", ex);
            }

            return downloadPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化下载路径失败");
            throw new InvalidOperationException("初始化下载路径失败", ex);
        }
    }
}
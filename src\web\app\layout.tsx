import type {Metadata} from "next";
import {Geist, <PERSON>eist_Mono} from "next/font/google";
import {Toaster} from "@/components/ui/sonner";
import "./globals.css";

const geistSans = Geist({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Geist_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

export const metadata: Metadata = {
    title: "YouTube Downloader - 免费在线视频下载工具",
    description:
        "免费的YouTube视频下载工具，支持MP4、MP3、字幕下载，批量下载播放列表，高清视频，快速安全。",
    keywords: "YouTube下载,视频下载,MP4下载,MP3下载,字幕下载,批量下载",
};

export default function RootLayout({
                                       children,
                                   }: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="zh-CN">
        <body
            className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
        {children}
        <Toaster/>
        </body>
        </html>
    );
}

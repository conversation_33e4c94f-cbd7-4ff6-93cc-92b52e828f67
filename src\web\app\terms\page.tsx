import {Metadata} from 'next'
import {Head<PERSON>} from '@/components/layout/Header'
import {Footer} from '@/components/layout/Footer'

export const metadata: Metadata = {
    title: 'Terms of Service | YouTube Downloader',
    description: 'Terms of Service for YouTube Downloader - Learn about the rules and guidelines for using our service.',
}

export default function TermsPage() {
    return (
        <div className="min-h-screen">
            <Header/>

            <div className="container mx-auto px-4 py-8 max-w-4xl">
                <div className="space-y-8">
                    <div className="text-center space-y-4">
                        <h1 className="text-4xl font-bold">使用条款</h1>
                        <p className="text-muted-foreground">
                            最后更新时间：{new Date().toLocaleDateString('zh-CN')}
                        </p>
                    </div>

                    <div className="prose max-w-none space-y-8">
                        <section>
                            <h2 className="text-2xl font-semibold mb-4">接受条款</h2>
                            <p className="text-muted-foreground">
                                通过访问和使用YouTube Downloader服务，您同意遵守本使用条款。如果您不同意这些条款，请不要使用我们的服务。
                            </p>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">服务描述</h2>
                            <p className="text-muted-foreground mb-4">我们提供以下服务：</p>
                            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                                <li>YouTube视频下载服务</li>
                                <li>音频提取和格式转换</li>
                                <li>字幕下载服务</li>
                                <li>批量下载功能</li>
                            </ul>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">使用限制</h2>
                            <p className="text-muted-foreground mb-4">您同意不会：</p>
                            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                                <li>下载受版权保护的内容用于商业用途</li>
                                <li>滥用或过度使用我们的服务</li>
                                <li>尝试破坏或干扰服务的正常运行</li>
                                <li>使用自动化工具大量下载内容</li>
                                <li>违反YouTube的服务条款</li>
                            </ul>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">版权声明</h2>
                            <p className="text-muted-foreground">
                                用户需自行确保下载的内容符合版权法律要求。我们不对用户下载的内容承担版权责任。
                                建议仅下载您拥有版权或已获得授权的内容。
                            </p>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">服务可用性</h2>
                            <p className="text-muted-foreground">
                                我们努力保持服务的可用性，但不保证服务不会中断。我们保留在必要时暂停或终止服务的权利。
                            </p>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">免责声明</h2>
                            <p className="text-muted-foreground">
                                本服务按"现状"提供，我们不对服务的准确性、可靠性或适用性做出任何保证。
                                用户使用服务的风险由用户自行承担。
                            </p>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">条款变更</h2>
                            <p className="text-muted-foreground">
                                我们可能会不时更新这些使用条款。重大变更将在网站上公布。
                                继续使用服务即表示您接受更新后的条款。
                            </p>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">联系我们</h2>
                            <p className="text-muted-foreground">
                                如果您对本使用条款有任何疑问，请联系我们：
                                <br/>
                                邮箱：<EMAIL>
                            </p>
                        </section>
                    </div>
                </div>
            </div>

            <Footer/>
        </div>
    )
}

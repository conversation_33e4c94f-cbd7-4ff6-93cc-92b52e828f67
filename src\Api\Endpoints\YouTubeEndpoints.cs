using System.Text.RegularExpressions;
using System.Web;
using Api.Extensions;
using Api.Services;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class YouTubeEndpoints
{
    public static void MapYouTubeEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/youtube").WithTags("YouTube");
        
        group.MapGet("/video/{videoId}", async (string videoId, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.GetVideoInfoAsync(videoId);
            return result.ToHttpResult();
        }).WithSummary("获取视频信息").WithDescription("获取指定视频ID的详细信息，包括视频流、音频流、字幕等");
    }
}
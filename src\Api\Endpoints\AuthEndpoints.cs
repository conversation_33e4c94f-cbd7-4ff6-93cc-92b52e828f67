using System.Security.Claims;
using Api.Data.Models;
using Api.Extensions;
using Api.Filters;
using Api.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class AuthEndpoints
{
    public static void MapAuthEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/auth").WithTags("Auth");

        group.MapPost("/register", async (RegisterRequest request, AuthService authService, HttpContext context, ClaimsPrincipal principal) =>
        {
            var anonymousId = principal.FindFirstValue(ClaimTypes.NameIdentifier);
            Guid.TryParse(anonymousId, out var anonymousUserId);
            var ipAddress = context.Connection.RemoteIpAddress?.ToString();

            var result = await authService.RegisterAsync(request, anonymousUserId, ipAddress);
            if (!result.IsSuccess) return result.ToHttpResult();

            await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            var user = result.Data!;
            await SignInUserAsync(context, user);
            await authService.RecordLoginSessionAsync(user.Id, ipAddress, context.Request.Headers.UserAgent.ToString());

            var userResponse = user.ToResponse();
            return TypedResults.Ok(ApiResponse<UserResponse>.Success(userResponse));
        }).WithValidation<RegisterRequest>().WithSummary("用户注册").WithDescription("处理新用户注册请求，创建账户并使用户登录。");

        group.MapPost("/login", async (LoginRequest request, AuthService authService, HttpContext context) =>
        {
            var ipAddress = context.Connection.RemoteIpAddress?.ToString();
            var result = await authService.LoginAsync(request, ipAddress);
            if (!result.IsSuccess) return result.ToHttpResult();

            var user = result.Data!;
            await SignInUserAsync(context, user);
            await authService.RecordLoginSessionAsync(user.Id, ipAddress, context.Request.Headers.UserAgent.ToString());

            var userResponse = user.ToResponse();
            return TypedResults.Ok(ApiResponse<UserResponse>.Success(userResponse));
        }).WithValidation<LoginRequest>().WithSummary("用户登录").WithDescription("验证用户凭据，成功后创建会话并返回用户信息。");

        group.MapPost("/logout", async (AuthService authService, HttpContext context, ClaimsPrincipal principal) =>
        {
            var userId = Guid.Parse(principal.FindFirstValue(ClaimTypes.NameIdentifier)!);
            var result = await authService.LogoutAsync(userId);
            if (result.IsSuccess) await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return result.ToHttpResult();
        }).RequireAuthorization("RegisteredUsersOnly").WithSummary("用户登出").WithDescription("注销当前用户会话。");

        group.MapGet("/me", (ClaimsPrincipal principal) =>
        {
            var userId = Guid.Parse(principal.FindFirstValue(ClaimTypes.NameIdentifier)!);
            var email = principal.FindFirstValue(ClaimTypes.Email);
            var userType = Enum.Parse<UserType>(principal.FindFirstValue("UserType")!);
            var planType = Enum.Parse<UserPlanType>(principal.FindFirstValue("PlanType")!);
            var currentUser = new CurrentUser(userId, userType, email, planType, userType == UserType.Anonymous);
            return TypedResults.Ok(ApiResponse<CurrentUser>.Success(currentUser));
        }).RequireAuthorization().WithSummary("获取当前用户信息").WithDescription("根据当前会话令牌获取已认证用户的详细信息。");

        group.MapPost("/change-password", async (ChangePasswordRequest request, AuthService authService, ClaimsPrincipal principal) =>
        {
            var userId = Guid.Parse(principal.FindFirstValue(ClaimTypes.NameIdentifier)!);
            var result = await authService.ChangePasswordAsync(userId, request);
            return result.ToHttpResult();
        }).RequireAuthorization("RegisteredUsersOnly").WithValidation<ChangePasswordRequest>().WithSummary("修改密码").WithDescription("修改已登录用户的密码。");

        group.MapPost("/forgot-password", async (ForgotPasswordRequest request, AuthService authService) =>
        {
            var result = await authService.ForgotPasswordAsync(request);
            return result.ToHttpResult();
        }).WithValidation<ForgotPasswordRequest>().WithSummary("忘记密码").WithDescription("为指定邮箱地址发起密码重置流程。");

        group.MapPost("/reset-password", async (ResetPasswordRequest request, AuthService authService) =>
        {
            var result = await authService.ResetPasswordAsync(request);
            return result.ToHttpResult();
        }).WithValidation<ResetPasswordRequest>().WithSummary("重置密码").WithDescription("使用收到的令牌来设置新密码。");

        group.MapPost("/verify-email", async (VerifyEmailRequest request, AuthService authService) =>
        {
            var result = await authService.VerifyEmailAsync(request);
            return result.ToHttpResult();
        }).WithValidation<VerifyEmailRequest>().WithSummary("验证邮箱地址").WithDescription("使用收到的令牌验证用户的邮箱地址。");

        group.MapPost("/resend-verification-email", async (AuthService authService, ClaimsPrincipal principal) =>
        {
            var userId = Guid.Parse(principal.FindFirstValue(ClaimTypes.NameIdentifier)!);
            var result = await authService.ResendVerificationEmailAsync(userId);
            return result.ToHttpResult();
        }).RequireAuthorization("RegisteredUsersOnly").WithSummary("重发邮箱验证邮件").WithDescription("为当前已登录的用户重新发送一封邮箱地址验证邮件。");
    }

    private static async Task SignInUserAsync(HttpContext context, User user)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Name, user.Email ?? user.Id.ToString()),
            new(ClaimTypes.Email, user.Email ?? string.Empty),
            new("UserType", user.UserType.ToString()),
            new("PlanType", user.PlanType.ToString()),
            new("ver", user.SecurityVersion.ToString())
        };

        var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
        var authProperties = new AuthenticationProperties
        {
            IsPersistent = true,
            ExpiresUtc = DateTimeOffset.UtcNow.AddDays(7)
        };

        await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity), authProperties);
    }
}
using Shared.Common;
using Shared.DTOs;
using Worker.Helpers;

namespace Worker.Services;

public class RapidApiService
{
    private readonly string? _apiHost;
    private readonly string? _apiKey;
    private readonly string _baseUrl;
    private readonly int _dailyLimit;
    private readonly HttpClient _httpClient;
    private readonly ILogger<RapidApiService> _logger;

    public RapidApiService(HttpClient httpClient, IConfiguration configuration, ILogger<RapidApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _apiKey = configuration["RapidApi:ApiKey"];
        _apiHost = configuration["RapidApi:Host"];
        _baseUrl = configuration["RapidApi:BaseUrl"] ?? "https://youtube-v31.p.rapidapi.com";
        _dailyLimit = int.Parse(configuration["RapidApi:DailyLimit"] ?? "1000");

        if (IsAvailable)
        {
            _httpClient.DefaultRequestHeaders.Add("X-RapidAPI-Key", _apiKey);
            _httpClient.DefaultRequestHeaders.Add("X-RapidAPI-Host", _apiHost);
        }
    }

    public bool IsAvailable => !string.IsNullOrEmpty(_apiKey) && !string.IsNullOrEmpty(_apiHost);
    public int CurrentRequestCount { get; private set; }

    public async Task<ServiceResult<YouTubeVideoResponse>> FetchVideoAsync(string videoId)
    {
        if (!IsAvailable)
            return ServiceResult<YouTubeVideoResponse>.Failure("RapidAPI 凭据未配置", "CONFIG_ERROR");

        if (CurrentRequestCount >= _dailyLimit)
            return ServiceResult<YouTubeVideoResponse>.Failure("每日API配额已超出", "QUOTA_EXCEEDED");

        try
        {
            CurrentRequestCount++;
            _logger.LogDebug("使用 RapidAPI 获取视频信息: {VideoId}", videoId);

            var url = $"{_baseUrl}/videos?part=snippet,statistics,contentDetails&id={videoId}";
            var result = await HttpHelper.GetJsonAsync<object>(_httpClient, url, _logger, "RapidAPI视频解析");

            if (!result.IsSuccess)
                return ServiceResult<YouTubeVideoResponse>.Failure(result.ErrorMessage!, result.ErrorCode);

            _logger.LogInformation("成功使用 RapidAPI 获取视频信息 {VideoId}", videoId);
            return ServiceResult<YouTubeVideoResponse>.Failure("RapidAPI 集成尚未实现", "NOT_IMPLEMENTED");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用 RapidAPI 获取视频信息时发生错误: {VideoId}", videoId);
            return ErrorHelper.HandleException<YouTubeVideoResponse>(ex, "RapidAPI视频解析");
        }
    }

    public Task<ServiceResult<YouTubePlaylistResponse>> FetchPlaylistAsync(string playlistId, int? maxVideos = null)
    {
        if (!IsAvailable)
            return Task.FromResult(ServiceResult<YouTubePlaylistResponse>.Failure("RapidAPI 凭据未配置", "CONFIG_ERROR"));

        if (CurrentRequestCount >= _dailyLimit)
            return Task.FromResult(ServiceResult<YouTubePlaylistResponse>.Failure("每日API配额已超出", "QUOTA_EXCEEDED"));

        try
        {
            CurrentRequestCount++;
            _logger.LogDebug("使用 RapidAPI 获取播放列表信息: {PlaylistId}", playlistId);

            return Task.FromResult(ServiceResult<YouTubePlaylistResponse>.Failure("RapidAPI 播放列表解析尚未实现", "NOT_IMPLEMENTED"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用 RapidAPI 获取播放列表信息时发生错误: {PlaylistId}", playlistId);
            return Task.FromResult(ErrorHelper.HandleException<YouTubePlaylistResponse>(ex, "RapidAPI播放列表解析"));
        }
    }

    public Task<ServiceResult<YouTubeChannelResponse>> FetchChannelAsync(string channelId, int? maxVideos = null)
    {
        if (!IsAvailable)
            return Task.FromResult(ServiceResult<YouTubeChannelResponse>.Failure("RapidAPI 凭据未配置", "CONFIG_ERROR"));

        if (CurrentRequestCount >= _dailyLimit)
            return Task.FromResult(ServiceResult<YouTubeChannelResponse>.Failure("每日API配额已超出", "QUOTA_EXCEEDED"));

        try
        {
            CurrentRequestCount++;
            _logger.LogDebug("使用 RapidAPI 获取频道信息: {ChannelId}", channelId);

            return Task.FromResult(ServiceResult<YouTubeChannelResponse>.Failure("RapidAPI 频道解析尚未实现", "NOT_IMPLEMENTED"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用 RapidAPI 获取频道信息时发生错误: {ChannelId}", channelId);
            return Task.FromResult(ErrorHelper.HandleException<YouTubeChannelResponse>(ex, "RapidAPI频道解析"));
        }
    }

    public async Task<bool> HealthCheckAsync()
    {
        if (!IsAvailable)
            return false;

        try
        {
            var url = $"{_baseUrl}/videos?part=snippet&id=dQw4w9WgXcQ";
            var response = await _httpClient.GetAsync(url);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    public void ResetRequestCount()
    {
        CurrentRequestCount = 0;
        _logger.LogInformation("RapidAPI 请求计数已重置");
    }
}
using System.Text.Json;
using Microsoft.AspNetCore.Diagnostics;
using Shared.Common;

namespace Api.Middleware;

public class GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger) : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
    {
        logger.LogError(exception, "请求处理过程中发生未处理的异常");

        httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
        httpContext.Response.ContentType = "application/json";

        var errorDetails = new ErrorDetails("发生意外的内部服务器错误，请稍后重试", nameof(ErrorType.Internal));
        var apiResponse = ApiResponse.Failure(errorDetails);

        var json = JsonSerializer.Serialize(apiResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await httpContext.Response.WriteAsync(json, cancellationToken);

        return true;
    }
}
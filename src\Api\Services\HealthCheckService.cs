using Api.Data;
using Microsoft.EntityFrameworkCore;
using Shared.Common;

namespace Api.Services;

/// <summary>
///     启动健康检查服务
/// </summary>
public class HealthCheckService
{
    private readonly IConfiguration _configuration;
    private readonly AppDbContext _dbContext;
    private readonly ILogger<HealthCheckService> _logger;

    public HealthCheckService(AppDbContext dbContext, IConfiguration configuration, ILogger<HealthCheckService> logger)
    {
        _dbContext = dbContext;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    ///     执行启动健康检查
    /// </summary>
    public async Task<ServiceResult> PerformStartupHealthCheckAsync()
    {
        var errors = new List<string>();

        try
        {
            _logger.LogInformation("开始执行启动健康检查...");

            // 1. 数据库连接检查
            await CheckDatabaseConnectionAsync(errors);

            // 2. 关键表存在性检查
            await CheckDatabaseTablesAsync(errors);

            // 3. RabbitMQ连接检查（可选，因为MassTransit会自动重连）
            CheckRabbitMQConfiguration(errors);

            if (errors.Any())
            {
                var errorMessage = string.Join(Environment.NewLine, errors);
                _logger.LogError("启动健康检查失败:{NewLine}{Errors}", Environment.NewLine, errorMessage);
                return ServiceResult.Failure($"启动健康检查失败: {errorMessage}");
            }

            _logger.LogInformation("启动健康检查通过");
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动健康检查过程中发生异常");
            return ServiceResult.Failure($"启动健康检查异常: {ex.Message}");
        }
    }

    private async Task CheckDatabaseConnectionAsync(List<string> errors)
    {
        try
        {
            _logger.LogDebug("检查数据库连接...");

            // 尝试连接数据库并执行简单查询
            await _dbContext.Database.CanConnectAsync();

            _logger.LogDebug("数据库连接正常");
        }
        catch (Exception ex)
        {
            errors.Add($"数据库连接失败: {ex.Message}");
        }
    }

    private async Task CheckDatabaseTablesAsync(List<string> errors)
    {
        try
        {
            _logger.LogDebug("检查数据库表结构...");

            // 检查关键表是否存在
            var tableChecks = new Dictionary<string, Func<Task<bool>>>
            {
                ["Users"] = async () => await _dbContext.Users.AnyAsync().ConfigureAwait(false) || true,
                ["WorkerNodes"] = async () => await _dbContext.Workers.AnyAsync().ConfigureAwait(false) || true,
                ["WorkerTasks"] = async () => await _dbContext.WorkerTasks.AnyAsync().ConfigureAwait(false) || true,
                ["Proxies"] = async () => await _dbContext.Proxies.AnyAsync().ConfigureAwait(false) || true
            };

            foreach (var (tableName, checkFunc) in tableChecks)
                try
                {
                    await checkFunc();
                    _logger.LogDebug("表 {TableName} 检查通过", tableName);
                }
                catch (Exception ex)
                {
                    errors.Add($"表 {tableName} 不存在或无法访问: {ex.Message}");
                }
        }
        catch (Exception ex)
        {
            errors.Add($"数据库表结构检查失败: {ex.Message}");
        }
    }

    private void CheckRabbitMQConfiguration(List<string> errors)
    {
        try
        {
            _logger.LogDebug("检查RabbitMQ配置...");

            var rabbitMQConnectionString = _configuration.GetConnectionString("RabbitMQ");
            if (string.IsNullOrEmpty(rabbitMQConnectionString))
            {
                errors.Add("RabbitMQ连接字符串未配置");
                return;
            }

            if (!rabbitMQConnectionString.StartsWith("amqp://"))
            {
                errors.Add("RabbitMQ连接字符串格式不正确");
                return;
            }

            _logger.LogDebug("RabbitMQ配置检查通过");
        }
        catch (Exception ex)
        {
            errors.Add($"RabbitMQ配置检查失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     获取系统状态摘要
    /// </summary>
    public async Task<object> GetSystemStatusAsync()
    {
        try
        {
            var dbConnected = false;
            var userCount = 0;
            var workerNodeCount = 0;
            var activeTaskCount = 0;

            try
            {
                dbConnected = await _dbContext.Database.CanConnectAsync();
                if (dbConnected)
                {
                    userCount = await _dbContext.Users.CountAsync();
                    workerNodeCount = await _dbContext.Workers.CountAsync();
                    activeTaskCount = await _dbContext.WorkerTasks.Where(t => t.Status == WorkerTaskStatus.Processing || t.Status == WorkerTaskStatus.Pending)
                        .CountAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取数据库统计信息时发生错误");
            }

            return new
            {
                DatabaseConnected = dbConnected,
                UserCount = userCount,
                WorkerNodeCount = workerNodeCount,
                ActiveTaskCount = activeTaskCount,
                Environment = _configuration["ASPNETCORE_ENVIRONMENT"] ?? "Production",
                StartupTime = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统状态时发生错误");
            return new
            {
                Error = ex.Message,
                StartupTime = DateTime.UtcNow
            };
        }
    }
}
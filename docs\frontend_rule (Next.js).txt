前端开发规范 (Next.js 15 App Router / React / TypeScript / Tailwind CSS)

1. 核心原则
RSC 优先： 默认 Server Components。仅交互、状态、浏览器 API 时使用 'use client'，Client Components 小且靠近叶子节点。
关注点分离： 清晰分离 UI、业务逻辑、状态。
移动优先： 使用 Tailwind 响应式设计。
可访问性 (A11y)： 遵循 WCAG AA，使用语义化 HTML 和 ARIA。
性能： 关注 Web Vitals，利用 Next.js 优化（缓存、代码分割、图片/字体），最小化客户端 JS。
函数式： 仅用函数组件和 Hooks，优先不可变性。

2. 项目结构与代码风格
命名： 目录/文件 (kebab-case), 组件/类型/接口 (PascalCase), 变量/函数/Hooks (camelCase), 常量 (UPPER_SNAKE_CASE)。
代码： 强制 Prettier & ESLint (next)。const 优先，使用现代 JS/TS。简洁、DRY。
注释： 复杂或非显而易见逻辑添加简洁中文注释（解释“为什么”）。
导出： 优先命名导出 (export function MyComponent)。

3. TypeScript 规范
严格模式： 必须启用 strict。
类型定义： Props, State, API 等定义明确 interface (优先) 或 type。禁止 any，用 unknown 或具体类型。
类型保护： 使用类型守卫。

4. React & Next.js (App Router) 组件与路由
组件： 小巧、单一职责，优先组合。Props 清晰、最小化。
Hooks： 遵循规则，封装复用逻辑到自定义 Hooks (use...)。
路由 & 布局： 文件系统路由 (app/)，next/link 导航，layout.tsx (共享 UI), template.tsx (状态保留)。
加载 & 错误： 实现 loading.tsx (Suspense), error.tsx (错误边界), notFound() (404)。
Metadata： 使用 generateMetadata API。
优化： next/image (提供宽高), next/font, next/dynamic (按需加载)。

5. 数据处理 (获取与变更)
服务端数据获取 (RSC)： Server Components 中强制优先使用 fetch() API (async/await fetch/库)，并利用 Next.js 缓存 (fetch 扩展, cache)。
客户端数据获取： Client Components 中，首选 Axios 进行数据获取（例如，动态数据、客户端独有交互触发、第三方 API）。应封装 Axios 实例，配置拦截器（认证、错误处理等）。
数据变更 (Mutations)： 必须使用 Server Actions 处理表单提交和 CUD 操作。严禁在 Server Actions 中使用 Axios 发送请求到后端 API（应直接在 Action 内部调用后端逻辑或使用 fetch）。
服务端验证： 必须在 Server Actions 中使用 Zod 严格验证。
类型安全 Actions： 确保 Server Actions 类型安全，考虑统一响应结构。
客户端缓存 (可选)： 复杂服务器状态管理使用 TanStack Query。

6. 状态管理
服务器状态： 主要通过 RSC 获取、URL 参数、Server Actions 管理。
客户端状态： 局部 (useState/useReducer), 简单跨组件 (useContext 谨慎), 全局/复杂 (Zustand)。
派生状态： 优先计算，性能瓶颈时用 useMemo。

7. 表单与验证
库： 推荐 React Hook Form。
Schema 验证： 必须使用 Zod 定义 Schema，用于客户端和服务端。
集成： 结合 React Hook Form 与 Server Actions。
状态与反馈 (React 19+)： 使用 useActionState 管理提交状态和错误。
用户反馈： 提供清晰的验证错误提示。

8. 样式 (Tailwind & Shadcn/ui)
UI 库： 主要使用 Shadcn/ui 组件进行组合。
自定义： 通过 globals.css 修改 Tailwind 配置和 CSS 变量。
Tailwind： 编写语义化、可维护的类名。谨慎用 @apply。避免内联 style (除非动态计算)。
CSS 变量： 优先使用 CSS 变量进行主题化和控制。

9. 性能优化 (额外)
最小化客户端 Bundle： 严格控制 'use client' 使用。
缓存： 理解并利用 Next.js 缓存 (Fetch, Full Route, Data)，使用 revalidatePath/Tag。
Memoization： 审慎使用 React.memo, useCallback, useMemo (基于性能分析)。
Suspense： 有效使用 <Suspense> 改善加载体验。

10. 可访问性 (A11y) 额外
测试： 使用 Axe DevTools 和手动测试（键盘、屏幕阅读器）。

11. 安全性
服务端验证： 所有用户输入必须在 Server Actions/Route Handlers 中用 Zod 验证。
XSS： 依赖 React 转义，谨慎用 dangerouslySetInnerHTML (需净化)。
认证/授权： 在服务器端强制检查。
环境变量： 不在客户端暴露敏感信息 (NEXT_PUBLIC_ 前缀用于客户端)。

12. 错误处理
预期错误 (Actions)： 作为 Action 返回值，由 useActionState 处理，不用 try/catch。
意外错误： error.tsx, global-error.tsx 捕获运行时错误。
函数错误： 使用卫语句/提前返回。Service 层可抛错或返回可辨识联合。实现服务端日志。

13. 国际化 (i18n)
库： 推荐 next-intl。
实现： 国际化所有面向用户的文本，处理区域设置、格式化等。
using System.Security.Cryptography;
using System.Text;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;

namespace Api.Services;

public class FileDownloadService
{
    private readonly IConfiguration _configuration;
    private readonly AppDbContext _dbContext;
    private readonly ILogger<FileDownloadService> _logger;
    private readonly string _secretKey;
    private readonly WorkerService _workerService;

    public FileDownloadService(AppDbContext dbContext, WorkerService workerService, ILogger<FileDownloadService> logger, IConfiguration configuration)
    {
        _dbContext = dbContext;
        _workerService = workerService;
        _logger = logger;
        _configuration = configuration;
        _secretKey = _configuration["FileDownload:SecretKey"] ?? "default-secret-key-change-in-production";
    }

    public async Task<ServiceResult<string>> GenerateDownloadUrlAsync(Guid taskId, Guid userId)
    {
        try
        {
            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId && wt.UserId == userId && wt.Status == WorkerTaskStatus.Completed)
                .FirstOrDefaultAsync();

            if (task == null)
                return ServiceResult<string>.Failure("任务不存在或无权限访问", "TASK_NOT_FOUND");

            if (string.IsNullOrEmpty(task.OutputFilePath))
                return ServiceResult<string>.Failure("任务未生成文件", "NO_FILE_GENERATED");

            if (!task.WorkerNodeId.HasValue)
                return ServiceResult<string>.Failure("无法确定文件所在的工作节点", "WORKER_NODE_NOT_FOUND");

            var workerNode = await _dbContext.WorkerNodes.Where(wn => wn.Id == task.WorkerNodeId.Value && wn.IsActive && wn.IsHealthy).FirstOrDefaultAsync();

            if (workerNode == null)
                return ServiceResult<string>.Failure("工作节点不可用", "WORKER_NODE_UNAVAILABLE");

            var downloadUrl = GenerateSignedDownloadUrl(workerNode.BaseUrl, task.OutputFilePath, taskId, userId, TimeSpan.FromHours(1));

            _logger.LogInformation("已为任务 {TaskId} 用户 {UserId} 生成下载链接", taskId, userId);
            return ServiceResult<string>.Success(downloadUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为任务 {TaskId} 生成下载链接时发生错误", taskId);
            return ServiceResult<string>.Failure("生成下载链接失败");
        }
    }

    public async Task<ServiceResult<FileDownloadInfo>> ValidateDownloadAsync(string signature, string filePath, Guid taskId, Guid userId, long timestamp)
    {
        try
        {
            var expectedSignature = GenerateSignature(filePath, taskId, userId, timestamp);
            if (signature != expectedSignature)
                return ServiceResult<FileDownloadInfo>.Failure("签名验证失败", "INVALID_SIGNATURE");

            var expirationTime = DateTimeOffset.FromUnixTimeSeconds(timestamp);
            if (DateTimeOffset.UtcNow > expirationTime)
                return ServiceResult<FileDownloadInfo>.Failure("下载链接已过期", "LINK_EXPIRED");

            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId && wt.UserId == userId && wt.Status == WorkerTaskStatus.Completed)
                .FirstOrDefaultAsync();

            if (task == null)
                return ServiceResult<FileDownloadInfo>.Failure("任务不存在或无权限访问", "TASK_NOT_FOUND");

            if (task.OutputFilePath != filePath)
                return ServiceResult<FileDownloadInfo>.Failure("文件路径不匹配", "FILE_PATH_MISMATCH");

            var downloadInfo = new FileDownloadInfo
            {
                TaskId = taskId,
                UserId = userId,
                FilePath = filePath,
                FileName = Path.GetFileName(filePath),
                FileSize = task.FileSize ?? 0,
                ContentType = GetContentType(filePath)
            };

            return ServiceResult<FileDownloadInfo>.Success(downloadInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证任务 {TaskId} 下载权限时发生错误", taskId);
            return ServiceResult<FileDownloadInfo>.Failure("验证下载权限失败");
        }
    }

    public async Task<ServiceResult<List<FileDownloadHistoryItem>>> GetDownloadHistoryAsync(Guid userId, int page = 1, int pageSize = 20)
    {
        try
        {
            var skip = (page - 1) * pageSize;

            var history = await _dbContext.WorkerTasks
                .Where(wt => wt.UserId == userId && wt.Status == WorkerTaskStatus.Completed && !string.IsNullOrEmpty(wt.OutputFilePath))
                .OrderByDescending(wt => wt.CompletedAt).Skip(skip).Take(pageSize).Select(wt => new FileDownloadHistoryItem
                {
                    TaskId = wt.Id,
                    TaskName = wt.Name,
                    VideoId = wt.VideoId,
                    VideoTitle = wt.VideoTitle,
                    FileName = Path.GetFileName(wt.OutputFilePath!),
                    FileSize = wt.FileSize ?? 0,
                    CompletedAt = wt.CompletedAt!.Value,
                    IsExpired = wt.FileExpiresAt.HasValue && wt.FileExpiresAt.Value < DateTime.UtcNow
                }).ToListAsync();

            return ServiceResult<List<FileDownloadHistoryItem>>.Success(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户 {UserId} 下载历史时发生错误", userId);
            return ServiceResult<List<FileDownloadHistoryItem>>.Failure("获取下载历史失败");
        }
    }

    private string GenerateSignedDownloadUrl(string workerBaseUrl, string filePath, Guid taskId, Guid userId, TimeSpan validFor)
    {
        var expirationTime = DateTimeOffset.UtcNow.Add(validFor);
        var timestamp = expirationTime.ToUnixTimeSeconds();
        var signature = GenerateSignature(filePath, taskId, userId, timestamp);

        var queryParams = new Dictionary<string, string>
        {
            ["signature"] = signature,
            ["taskId"] = taskId.ToString(),
            ["userId"] = userId.ToString(),
            ["timestamp"] = timestamp.ToString(),
            ["filePath"] = filePath
        };

        var queryString = string.Join("&", queryParams.Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value)}"));
        return $"{workerBaseUrl.TrimEnd('/')}/download?{queryString}";
    }

    private string GenerateSignature(string filePath, Guid taskId, Guid userId, long timestamp)
    {
        var data = $"{filePath}|{taskId}|{userId}|{timestamp}";
        var keyBytes = Encoding.UTF8.GetBytes(_secretKey);
        var dataBytes = Encoding.UTF8.GetBytes(data);

        using var hmac = new HMACSHA256(keyBytes);
        var hashBytes = hmac.ComputeHash(dataBytes);
        return Convert.ToBase64String(hashBytes);
    }

    private static string GetContentType(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension switch
        {
            ".mp4" => "video/mp4",
            ".webm" => "video/webm",
            ".mkv" => "video/x-matroska",
            ".mp3" => "audio/mpeg",
            ".m4a" => "audio/mp4",
            ".wav" => "audio/wav",
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".txt" => "text/plain",
            ".srt" => "text/plain",
            ".vtt" => "text/vtt",
            _ => "application/octet-stream"
        };
    }

    // ==================== 新增方法 ====================

    public async Task<ServiceResult<DownloadLinkResponse>> GenerateDownloadLinkAsync(Guid taskId, Guid userId, TimeSpan validFor)
    {
        try
        {
            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId && wt.UserId == userId && wt.Status == WorkerTaskStatus.Completed)
                .FirstOrDefaultAsync();

            if (task == null)
                return ServiceResult<DownloadLinkResponse>.Failure("任务不存在或无权限访问", "TASK_NOT_FOUND");

            if (string.IsNullOrEmpty(task.OutputFilePath))
                return ServiceResult<DownloadLinkResponse>.Failure("任务未生成文件", "NO_FILE_GENERATED");

            if (!task.WorkerNodeId.HasValue)
                return ServiceResult<DownloadLinkResponse>.Failure("无法确定文件所在的工作节点", "WORKER_NODE_NOT_FOUND");

            var workerNode = await _dbContext.WorkerNodes.Where(wn => wn.Id == task.WorkerNodeId.Value && wn.IsActive && wn.IsHealthy).FirstOrDefaultAsync();

            if (workerNode == null)
                return ServiceResult<DownloadLinkResponse>.Failure("工作节点不可用", "WORKER_NODE_UNAVAILABLE");

            var downloadUrl = GenerateSignedDownloadUrl(workerNode.BaseUrl, task.OutputFilePath, taskId, userId, validFor);

            var expiresAt = DateTime.UtcNow.Add(validFor);
            var response = new DownloadLinkResponse(downloadUrl, expiresAt, Path.GetFileName(task.OutputFilePath), task.FileSize ?? 0,
                GetContentType(task.OutputFilePath));

            _logger.LogInformation("已为任务 {TaskId} 用户 {UserId} 生成自定义下载链接，有效期 {ValidFor}", taskId, userId, validFor);
            return ServiceResult<DownloadLinkResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为任务 {TaskId} 生成自定义下载链接时发生错误", taskId);
            return ServiceResult<DownloadLinkResponse>.Failure("生成下载链接失败");
        }
    }

    public async Task<ServiceResult<FileInfoResponse>> GetFileInfoAsync(Guid taskId, Guid userId)
    {
        try
        {
            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId && wt.UserId == userId && wt.Status == WorkerTaskStatus.Completed)
                .FirstOrDefaultAsync();

            if (task == null)
                return ServiceResult<FileInfoResponse>.Failure("任务不存在或无权限访问", "TASK_NOT_FOUND");

            var hasFile = !string.IsNullOrEmpty(task.OutputFilePath);
            string? downloadUrl = null;

            if (hasFile && task.WorkerNodeId.HasValue)
            {
                var urlResult = await GenerateDownloadUrlAsync(taskId, userId);
                downloadUrl = urlResult.IsSuccess ? urlResult.Data : null;
            }

            var fileInfo = new FileInfoResponse(taskId, hasFile, downloadUrl, hasFile ? Path.GetFileName(task.OutputFilePath!) : null, task.FileSize,
                hasFile ? GetContentType(task.OutputFilePath!) : null, task.FileExpiresAt);

            return ServiceResult<FileInfoResponse>.Success(fileInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务 {TaskId} 文件信息时发生错误", taskId);
            return ServiceResult<FileInfoResponse>.Failure("获取文件信息失败");
        }
    }

    public async Task<ServiceResult<FileStatsResponse>> GetFileStatsAsync(Guid userId)
    {
        try
        {
            var tasks = await _dbContext.WorkerTasks
                .Where(wt => wt.UserId == userId && wt.Status == WorkerTaskStatus.Completed && !string.IsNullOrEmpty(wt.OutputFilePath)).ToListAsync();

            var totalFiles = tasks.Count;
            var totalSize = tasks.Sum(t => t.FileSize ?? 0);
            var expiredFiles = tasks.Count(t => t.FileExpiresAt.HasValue && t.FileExpiresAt.Value < DateTime.UtcNow);
            var lastDownload = tasks.Max(t => t.CompletedAt) ?? DateTime.MinValue;

            // 按文件类型分类
            var videoFiles = tasks.Count(t => IsVideoFile(t.OutputFilePath!));
            var audioFiles = tasks.Count(t => IsAudioFile(t.OutputFilePath!));
            var subtitleFiles = tasks.Count(t => IsSubtitleFile(t.OutputFilePath!));
            var thumbnailFiles = tasks.Count(t => IsThumbnailFile(t.OutputFilePath!));

            var stats = new FileStatsResponse(totalFiles, totalSize, videoFiles, audioFiles, subtitleFiles, thumbnailFiles, expiredFiles, lastDownload);

            return ServiceResult<FileStatsResponse>.Success(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户 {UserId} 文件统计时发生错误", userId);
            return ServiceResult<FileStatsResponse>.Failure("获取文件统计失败");
        }
    }

    public async Task<ServiceResult<List<FileDownloadHistoryItem>>> SearchDownloadHistoryAsync(Guid userId, DownloadHistoryRequest request)
    {
        try
        {
            var query = _dbContext.WorkerTasks.Where(wt =>
                wt.UserId == userId && wt.Status == WorkerTaskStatus.Completed && !string.IsNullOrEmpty(wt.OutputFilePath));

            // 按文件类型过滤
            if (!string.IsNullOrEmpty(request.FileType))
                query = request.FileType.ToLower() switch
                {
                    "video" => query.Where(wt => IsVideoFile(wt.OutputFilePath!)),
                    "audio" => query.Where(wt => IsAudioFile(wt.OutputFilePath!)),
                    "subtitle" => query.Where(wt => IsSubtitleFile(wt.OutputFilePath!)),
                    "thumbnail" => query.Where(wt => IsThumbnailFile(wt.OutputFilePath!)),
                    _ => query
                };

            // 按时间范围过滤
            if (request.StartDate.HasValue)
                query = query.Where(wt => wt.CompletedAt >= request.StartDate.Value);

            if (request.EndDate.HasValue)
                query = query.Where(wt => wt.CompletedAt <= request.EndDate.Value);

            var skip = (request.Page - 1) * request.PageSize;
            var history = await query.OrderByDescending(wt => wt.CompletedAt).Skip(skip).Take(request.PageSize).Select(wt =>
                new FileDownloadHistoryItem(wt.Id, wt.Name, wt.VideoId, wt.VideoTitle, Path.GetFileName(wt.OutputFilePath!), wt.FileSize ?? 0,
                    wt.CompletedAt!.Value, wt.FileExpiresAt.HasValue && wt.FileExpiresAt.Value < DateTime.UtcNow)).ToListAsync();

            return ServiceResult<List<FileDownloadHistoryItem>>.Success(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索用户 {UserId} 下载历史时发生错误", userId);
            return ServiceResult<List<FileDownloadHistoryItem>>.Failure("搜索下载历史失败");
        }
    }

    public async Task<ServiceResult<FileCleanupResponse>> CleanupFilesAsync(Guid userId, FileCleanupRequest request)
    {
        try
        {
            var query = _dbContext.WorkerTasks.Where(wt => wt.UserId == userId && !string.IsNullOrEmpty(wt.OutputFilePath));

            var tasksToClean = new List<WorkerTask>();

            if (request.CleanExpiredFiles)
            {
                var expiredTasks = await query.Where(wt => wt.FileExpiresAt.HasValue && wt.FileExpiresAt.Value < DateTime.UtcNow).ToListAsync();
                tasksToClean.AddRange(expiredTasks);
            }

            if (request.CleanFailedTasks)
            {
                var failedTasks = await query.Where(wt => wt.Status == WorkerTaskStatus.Failed).ToListAsync();
                tasksToClean.AddRange(failedTasks);
            }

            if (request.OlderThan.HasValue)
            {
                var oldTasks = await query.Where(wt => wt.CompletedAt < request.OlderThan.Value).ToListAsync();
                tasksToClean.AddRange(oldTasks);
            }

            // 去重
            tasksToClean = tasksToClean.Distinct().ToList();

            var cleanedFiles = 0;
            var freedSpace = 0L;
            var cleanedFileTypes = new HashSet<string>();

            foreach (var task in tasksToClean)
                if (!string.IsNullOrEmpty(task.OutputFilePath))
                {
                    cleanedFiles++;
                    freedSpace += task.FileSize ?? 0;

                    var extension = Path.GetExtension(task.OutputFilePath).ToLowerInvariant();
                    if (!string.IsNullOrEmpty(extension))
                        cleanedFileTypes.Add(extension);

                    // 清除文件路径，但保留任务记录
                    task.OutputFilePath = null;
                    task.FileSize = null;
                    task.FileExpiresAt = null;
                }

            await _dbContext.SaveChangesAsync();

            var response = new FileCleanupResponse(cleanedFiles, freedSpace, cleanedFileTypes.ToList());

            _logger.LogInformation("用户 {UserId} 清理了 {CleanedFiles} 个文件，释放 {FreedSpace} 字节空间", userId, cleanedFiles, freedSpace);

            return ServiceResult<FileCleanupResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理用户 {UserId} 文件时发生错误", userId);
            return ServiceResult<FileCleanupResponse>.Failure("清理文件失败");
        }
    }

    private static bool IsVideoFile(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension is ".mp4" or ".webm" or ".mkv" or ".avi" or ".mov";
    }

    private static bool IsAudioFile(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension is ".mp3" or ".m4a" or ".wav" or ".aac" or ".opus";
    }

    private static bool IsSubtitleFile(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension is ".srt" or ".vtt" or ".ass" or ".ssa";
    }

    private static bool IsThumbnailFile(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension is ".jpg" or ".jpeg" or ".png" or ".webp";
    }
}
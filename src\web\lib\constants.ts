export const YOUTUBE_CONSTANTS = {
    MAX_MULTI_VIDEOS: 100,
    MIN_MULTI_VIDEOS: 2,
    VIDEO_ID_LENGTH: 11,
    SUPPORTED_DOMAINS: ['youtube.com', 'youtu.be'],
} as const;

export const UI_CONSTANTS = {
    DEBOUNCE_DELAY: 300,
    LOADING_DELAY: 500,
    ERROR_DISPLAY_DURATION: 5000,
    SUCCESS_DISPLAY_DURATION: 3000,
} as const;

export const VALIDATION_MESSAGES = {
    EMPTY_URL: '请输入YouTube链接',
    INVALID_URL: '请输入有效的YouTube链接',
    INVALID_FORMAT: '无法识别的YouTube链接格式',
    NOT_YOUTUBE: '请输入YouTube链接',
    TOO_MANY_VIDEOS: `最多支持${YOUTUBE_CONSTANTS.MAX_MULTI_VIDEOS}个视频`,
    TOO_FEW_VIDEOS: `至少需要${YOUTUBE_CONSTANTS.MIN_MULTI_VIDEOS}个视频`,
    NO_VALID_VIDEOS: '没有找到有效的视频链接',
    FILE_READ_ERROR: '文件读取失败',
    FILE_FORMAT_ERROR: '文件格式不正确，请上传TXT文件',
} as const;

export const ROUTES = {
    HOME: '/',
    VIDEO_DOWNLOAD: '/download',
    BATCH_DOWNLOAD: '/batch-download',
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    PRICING: '/pricing',
    TERMS: '/terms',
    PRIVACY: '/privacy',
} as const;

export const API_ENDPOINTS = {
    YOUTUBE: {
        PARSE_URL: '/api/youtube/parse-url',
        FETCH: '/api/youtube/fetch',
        VIDEO: (videoId: string) => `/api/youtube/video/${videoId}`,
        PLAYLIST: (playlistId: string) => `/api/youtube/playlist/${playlistId}`,
        CHANNEL: (channelId: string) => `/api/youtube/channel/${channelId}`,
    },
    TASKS: {
        CREATE_BATCH: '/api/tasks/batch',
        LIST: '/api/tasks',
        DETAIL: (taskId: string) => `/api/tasks/${taskId}`,
    },
    AUTH: {
        LOGIN: '/api/auth/login',
        REGISTER: '/api/auth/register',
        LOGOUT: '/api/auth/logout',
        CHECK: '/api/auth/check',
        ME: '/api/auth/me',
    },
} as const;

export const FILE_CONSTANTS = {
    SUPPORTED_TYPES: ['text/plain'],
    MAX_FILE_SIZE: 1024 * 1024, // 1MB
    ENCODING: 'UTF-8',
} as const;

export const LINK_TYPES = {
    VIDEO: 'video',
    PLAYLIST: 'playlist',
    CHANNEL: 'channel',
    INVALID: 'invalid',
} as const;

export type LinkType = typeof LINK_TYPES[keyof typeof LINK_TYPES];

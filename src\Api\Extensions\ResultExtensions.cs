using System.Net;
using Shared.Common;

namespace Api.Extensions;

public static class ResultExtensions
{
    private static readonly IReadOnlyDictionary<ErrorType, (HttpStatusCode StatusCode, string DefaultMessage)> ErrorMapping =
        new Dictionary<ErrorType, (HttpStatusCode, string)>
        {
            [ErrorType.Internal] = (HttpStatusCode.InternalServerError, "发生意外的内部服务器错误"),
            [ErrorType.Validation] = (HttpStatusCode.UnprocessableEntity, "发生一个或多个验证错误"),
            [ErrorType.Unauthorized] = (HttpStatusCode.Unauthorized, "认证失败，请重新登录"),
            [ErrorType.Forbidden] = (HttpStatusCode.Forbidden, "您没有权限执行此操作"),
            [ErrorType.NotFound] = (HttpStatusCode.NotFound, "未找到请求的资源"),
            [ErrorType.Conflict] = (HttpStatusCode.Conflict, "资源当前状态发生冲突"),
            [ErrorType.TooManyRequests] = (HttpStatusCode.TooManyRequests, "请求过于频繁，请稍后重试"),
            [ErrorType.ServiceUnavailable] = (HttpStatusCode.ServiceUnavailable, "服务暂时不可用，请稍后重试"),
            [ErrorType.UserQuotaExceeded] = (HttpStatusCode.Forbidden, "用户配额已超限"),
            [ErrorType.PlanQuotaExceeded] = (HttpStatusCode.Forbidden, "套餐配额已超限"),
            [ErrorType.TaskQueueFull] = (HttpStatusCode.ServiceUnavailable, "任务队列已满"),
            [ErrorType.UnsupportedTaskType] = (HttpStatusCode.BadRequest, "不支持的任务类型"),
            [ErrorType.WorkerOverloaded] = (HttpStatusCode.ServiceUnavailable, "工作节点过载"),
            [ErrorType.WorkerProcessingError] = (HttpStatusCode.InternalServerError, "工作节点处理错误"),
            [ErrorType.FileTooLarge] = (HttpStatusCode.BadRequest, "文件过大"),
            [ErrorType.FileExpired] = (HttpStatusCode.Gone, "文件已过期"),
            [ErrorType.FileProcessingError] = (HttpStatusCode.InternalServerError, "文件处理错误"),
            [ErrorType.ProxyError] = (HttpStatusCode.ServiceUnavailable, "代理服务错误")
        };

    public static IResult ToHttpResult(this ServiceResult result, HttpStatusCode successStatusCode = HttpStatusCode.OK)
    {
        return result.IsSuccess ? CreateSuccessResult(successStatusCode) : ConvertErrorToHttpResult(result.Error, result.ValidationErrors);
    }

    public static IResult ToHttpResult<T>(this ServiceResult<T> result, HttpStatusCode successStatusCode = HttpStatusCode.OK)
    {
        return result.IsSuccess ? CreateSuccessResult(result.Data, successStatusCode) : ConvertErrorToHttpResult(result.Error, result.ValidationErrors);
    }

    private static IResult CreateSuccessResult(HttpStatusCode statusCode)
    {
        return statusCode switch
        {
            HttpStatusCode.OK => TypedResults.Ok(ApiResponse.Success()),
            HttpStatusCode.Created => TypedResults.Created(string.Empty, ApiResponse.Success()),
            HttpStatusCode.NoContent => TypedResults.NoContent(),
            _ => TypedResults.Ok(ApiResponse.Success())
        };
    }

    private static IResult CreateSuccessResult<T>(T? data, HttpStatusCode statusCode)
    {
        if (data is null) return TypedResults.NoContent();

        var response = ApiResponse<T>.Success(data);
        return statusCode switch
        {
            HttpStatusCode.OK => TypedResults.Ok(response),
            HttpStatusCode.Created => TypedResults.Created(string.Empty, response),
            _ => TypedResults.Ok(response)
        };
    }

    private static IResult ConvertErrorToHttpResult(Error error, List<ValidationError>? validationErrors)
    {
        var (statusCode, defaultMessage) = ErrorMapping.GetValueOrDefault(error.Type, (HttpStatusCode.BadRequest, "请求无效"));

        var errorDetails = new ErrorDetails(error.DeveloperMessage ?? defaultMessage, error.Type.ToString(), error.Key, error.Params, validationErrors);
        var response = ApiResponse.Failure(errorDetails);

        return statusCode switch
        {
            HttpStatusCode.NotFound => TypedResults.NotFound(response),
            HttpStatusCode.Conflict => TypedResults.Conflict(response),
            HttpStatusCode.Forbidden => TypedResults.Json(response, statusCode: (int)HttpStatusCode.Forbidden),
            HttpStatusCode.Unauthorized => TypedResults.Unauthorized(),
            HttpStatusCode.UnprocessableEntity => TypedResults.UnprocessableEntity(response),
            HttpStatusCode.InternalServerError => TypedResults.Problem(response.Error?.Message, statusCode: (int)statusCode),
            HttpStatusCode.ServiceUnavailable => TypedResults.Json(response, statusCode: (int)statusCode),
            HttpStatusCode.TooManyRequests => TypedResults.Json(response, statusCode: (int)statusCode),
            HttpStatusCode.Gone => TypedResults.Json(response, statusCode: (int)statusCode),
            _ => TypedResults.BadRequest(response)
        };
    }
}
using Shared.Common;
using Shared.DTOs;

namespace Worker.Services;

public class YouTubeService
{
    private readonly ILogger<YouTubeService> _logger;
    private readonly RapidApiService _rapidApiService;
    private readonly YtDlpService _ytDlpService;

    public YouTubeService(YtDlpService ytDlpService, RapidApiService rapidApiService, ILogger<YouTubeService> logger)
    {
        _ytDlpService = ytDlpService;
        _rapidApiService = rapidApiService;
        _logger = logger;
        _logger.LogInformation("已初始化YouTube服务");
    }

    public async Task<ServiceResult<YouTubeVideoResponse>> FetchVideoAsync(string videoId, ProxyInfo? proxy = null)
    {
        var result = await _ytDlpService.FetchVideoAsync(videoId, proxy);

        if (result.IsSuccess)
        {
            _logger.LogDebug("成功使用 yt-dlp 获取视频 {VideoId} 信息", videoId);
            return result;
        }

        _logger.LogWarning("yt-dlp 获取视频 {VideoId} 失败: {Error}", videoId, result.ErrorMessage);

        if (_rapidApiService.IsAvailable)
            try
            {
                var rapidResult = await _rapidApiService.FetchVideoAsync(videoId);
                if (rapidResult.IsSuccess)
                {
                    _logger.LogDebug("成功使用 RapidAPI 获取视频 {VideoId} 信息", videoId);
                    return rapidResult;
                }

                _logger.LogWarning("RapidAPI 获取视频 {VideoId} 失败: {Error}", videoId, rapidResult.ErrorMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用 RapidAPI 获取视频 {VideoId} 时发生错误", videoId);
            }

        return ServiceResult<YouTubeVideoResponse>.Failure("所有YouTube API提供商都失败了");
    }

    public async Task<ServiceResult<YouTubePlaylistResponse>> FetchPlaylistAsync(string playlistId, ProxyInfo? proxy = null, int? maxVideos = null)
    {
        var result = await _ytDlpService.FetchPlaylistAsync(playlistId, proxy, maxVideos);

        if (result.IsSuccess)
        {
            _logger.LogDebug("成功使用 yt-dlp 获取播放列表 {PlaylistId} 信息", playlistId);
            return result;
        }

        _logger.LogWarning("yt-dlp 获取播放列表 {PlaylistId} 失败: {Error}", playlistId, result.ErrorMessage);

        if (_rapidApiService.IsAvailable)
            try
            {
                var rapidResult = await _rapidApiService.FetchPlaylistAsync(playlistId, maxVideos);
                if (rapidResult.IsSuccess)
                {
                    _logger.LogDebug("成功使用 RapidAPI 获取播放列表 {PlaylistId} 信息", playlistId);
                    return rapidResult;
                }

                _logger.LogWarning("RapidAPI 获取播放列表 {PlaylistId} 失败: {Error}", playlistId, rapidResult.ErrorMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用 RapidAPI 获取播放列表 {PlaylistId} 时发生错误", playlistId);
            }

        return ServiceResult<YouTubePlaylistResponse>.Failure("所有YouTube API提供商都失败了");
    }

    public async Task<ServiceResult<YouTubeChannelResponse>> FetchChannelAsync(string channelId, ProxyInfo? proxy = null, int? maxVideos = null)
    {
        var result = await _ytDlpService.FetchChannelAsync(channelId, proxy, maxVideos);

        if (result.IsSuccess)
        {
            _logger.LogDebug("成功使用 yt-dlp 获取频道 {ChannelId} 信息", channelId);
            return result;
        }

        _logger.LogWarning("yt-dlp 获取频道 {ChannelId} 失败: {Error}", channelId, result.ErrorMessage);

        if (_rapidApiService.IsAvailable)
            try
            {
                var rapidResult = await _rapidApiService.FetchChannelAsync(channelId, maxVideos);
                if (rapidResult.IsSuccess)
                {
                    _logger.LogDebug("成功使用 RapidAPI 获取频道 {ChannelId} 信息", channelId);
                    return rapidResult;
                }

                _logger.LogWarning("RapidAPI 获取频道 {ChannelId} 失败: {Error}", channelId, rapidResult.ErrorMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用 RapidAPI 获取频道 {ChannelId} 时发生错误", channelId);
            }

        return ServiceResult<YouTubeChannelResponse>.Failure("所有YouTube API提供商都失败了");
    }

    public async Task<ServiceResult<YouTubeVideoResponse>> ParseVideoWithProviderAsync(string providerName, string videoId, ProxyInfo? proxy = null)
    {
        return providerName.ToLower() switch
        {
            "yt-dlp" => await _ytDlpService.FetchVideoAsync(videoId, proxy),
            "rapidapi" => _rapidApiService.IsAvailable
                ? await _rapidApiService.FetchVideoAsync(videoId)
                : ServiceResult<YouTubeVideoResponse>.Failure("RapidAPI 不可用"),
            _ => ServiceResult<YouTubeVideoResponse>.Failure($"未知的提供商: {providerName}")
        };
    }

    public async Task<Dictionary<string, bool>> GetProvidersHealthAsync()
    {
        var health = new Dictionary<string, bool>
        {
            ["yt-dlp"] = await _ytDlpService.IsYtDlpAvailableAsync()
        };

        if (_rapidApiService.IsAvailable)
            try
            {
                health["rapidapi"] = await _rapidApiService.HealthCheckAsync();
            }
            catch
            {
                health["rapidapi"] = false;
            }
        else
            health["rapidapi"] = false;

        return health;
    }

    public Dictionary<string, object> GetProvidersInfo()
    {
        return new Dictionary<string, object>
        {
            ["yt-dlp"] = new { Name = "yt-dlp", Available = true, Priority = 1 },
            ["rapidapi"] = new { Name = "RapidAPI", Available = _rapidApiService.IsAvailable, Priority = 2 }
        };
    }

    public Dictionary<string, object> GetUsageStats()
    {
        return new Dictionary<string, object>
        {
            ["rapidapi"] = new { RequestCount = _rapidApiService.CurrentRequestCount }
        };
    }
}
﻿using Api.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace Api.Data;

public class AppDbContext(DbContextOptions<AppDbContext> options) : DbContext(options)
{
    public DbSet<User> Users { get; set; }
    public DbSet<UserSession> UserSessions { get; set; }
    public DbSet<WorkerTask> WorkerTasks { get; set; }
    public DbSet<BatchTask> BatchTasks { get; set; }
    public DbSet<Proxy> Proxies { get; set; }
    public DbSet<Worker> Workers { get; set; }
    public DbSet<WorkerMetrics> WorkerMetrics { get; set; }
    public DbSet<WorkerAlert> WorkerAlerts { get; set; }
    public DbSet<YouTubeVideo> YouTubeVideos { get; set; }
    public DbSet<YouTubeChannel> YouTubeChannels { get; set; }
    public DbSet<YouTubePlaylist> YouTubePlaylists { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(AppDbContext).Assembly);
    }
}
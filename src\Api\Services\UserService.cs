using System.Security.Claims;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class UserService(AppDbContext dbContext, ILogger<UserService> logger)
{
    public async Task<ServiceResult<User>> GetOrCreateUserAsync(ClaimsPrincipal principal)
    {
        var userIdString = principal.FindFirstValue(ClaimTypes.NameIdentifier);
        if (!Guid.TryParse(userIdString, out var userId))
        {
            logger.LogError("ClaimsPrincipal 缺少有效的用户ID: {UserIdString}", userIdString);
            return ServiceResult<User>.Failure(new Error(ErrorType.Validation, ErrorKeys.INVALID_CREDENTIALS,
                DeveloperMessage: $"ClaimsPrincipal 缺少有效的用户ID: '{userIdString}'。"));
        }

        var user = await dbContext.Users.FindAsync(userId);
        if (user != null) return ServiceResult<User>.Success(user);

        var userTypeString = principal.FindFirstValue("UserType");
        if (Enum.TryParse<UserType>(userTypeString, out var userType) && userType == UserType.Anonymous)
        {
            var planTypeString = principal.FindFirstValue("PlanType");
            if (!Enum.TryParse<UserPlanType>(planTypeString, out var planType))
            {
                planType = UserPlanType.Free;
            }

            var newUser = new User
            {
                Id = userId,
                UserType = UserType.Anonymous,
                PlanType = planType,
                Status = UserAccountStatus.Active,
                LastActiveAt = DateTime.UtcNow
            };

            dbContext.Users.Add(newUser);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("匿名用户即时创建成功: {UserId}", userId);
            return ServiceResult<User>.Success(newUser);
        }

        logger.LogWarning("用户ID {UserId} 在数据库中未找到，且无法作为匿名用户创建。可能为无效会话。", userId);
        return ServiceResult<User>.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.INVALID_CREDENTIALS,
            DeveloperMessage: $"用户ID '{userId}' 在数据库中未找到，且无法作为匿名用户创建。可能为无效会话。"));
    }

    public async Task<ServiceResult<AdminUserListResponse>> GetUsersAsync(int page, int pageSize, string? search, UserType? userType, UserAccountStatus? status)
    {
        try
        {
            var query = dbContext.Users.AsQueryable();

            if (!string.IsNullOrWhiteSpace(search))
            {
                query = query.Where(u => u.Email != null && u.Email.Contains(search));
            }

            if (userType.HasValue)
            {
                query = query.Where(u => u.UserType == userType.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(u => u.Status == status.Value);
            }

            var totalCount = await query.CountAsync();
            var skip = (page - 1) * pageSize;

            var users = await query
                .OrderByDescending(u => u.CreatedAt)
                .Skip(skip)
                .Take(pageSize)
                .Select(u => new AdminUserListItem(
                    u.Id,
                    u.UserType,
                    u.Email,
                    u.PlanType,
                    u.PlanExpiresAt,
                    u.Status,
                    u.EmailVerified,
                    u.LastLoginAt,
                    u.LastLoginIp,
                    u.CreatedAt,
                    u.LastActiveAt))
                .ToListAsync();

            var response = new AdminUserListResponse(users, totalCount, page, pageSize);
            return ServiceResult<AdminUserListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户列表时发生错误");
            return ServiceResult<AdminUserListResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<AdminUserDetailResponse>> GetUserDetailAsync(Guid userId)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult<AdminUserDetailResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            var totalTasks = await dbContext.WorkerTasks.CountAsync(t => t.UserId == userId);
            var completedTasks = await dbContext.WorkerTasks.CountAsync(t => t.UserId == userId && t.Status == WorkerTaskStatus.Completed);
            var failedTasks = await dbContext.WorkerTasks.CountAsync(t => t.UserId == userId && t.Status == WorkerTaskStatus.Failed);
            var activeSessions = await dbContext.UserSessions.CountAsync(s => s.UserId == userId && s.ExpiresAt > DateTime.UtcNow);
            var totalDownloadSize = await dbContext.WorkerTasks
                .Where(t => t.UserId == userId && t.FileSize.HasValue)
                .SumAsync(t => t.FileSize!.Value);
            var lastTaskAt = await dbContext.WorkerTasks
                .Where(t => t.UserId == userId)
                .OrderByDescending(t => t.CreatedAt)
                .Select(t => (DateTime?)t.CreatedAt)
                .FirstOrDefaultAsync();

            var stats = new AdminUserStatsResponse(
                totalTasks,
                completedTasks,
                failedTasks,
                activeSessions,
                totalDownloadSize,
                lastTaskAt);

            var response = new AdminUserDetailResponse(
                user.Id,
                user.UserType,
                user.Email,
                user.PlanType,
                user.PlanExpiresAt,
                user.Status,
                user.EmailVerified,
                user.EmailVerificationToken,
                user.EmailVerificationTokenExpiresAt,
                user.PasswordResetToken,
                user.PasswordResetTokenExpiresAt,
                user.SecurityVersion,
                user.LoginFailureCount,
                user.AccountLockedUntil,
                user.LastLoginAt,
                user.LastLoginIp,
                user.CreatedAt,
                user.LastActiveAt,
                user.UpdatedAt,
                stats);

            return ServiceResult<AdminUserDetailResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户详情时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<AdminUserDetailResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult> DisableUserAsync(Guid userId)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            user.Status = UserAccountStatus.Disabled;
            user.UpdatedAt = DateTime.UtcNow;
            user.SecurityVersion++;

            await dbContext.SaveChangesAsync();

            logger.LogInformation("用户已被禁用: {UserId}", userId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "禁用用户时发生错误，用户ID: {UserId}", userId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult> EnableUserAsync(Guid userId)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            user.Status = UserAccountStatus.Active;
            user.UpdatedAt = DateTime.UtcNow;
            user.AccountLockedUntil = null;
            user.LoginFailureCount = 0;

            await dbContext.SaveChangesAsync();

            logger.LogInformation("用户已被启用: {UserId}", userId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "启用用户时发生错误，用户ID: {UserId}", userId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<ResetPasswordResponse>> ResetUserPasswordAsync(Guid userId)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult<ResetPasswordResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            var newPassword = GenerateRandomPassword();
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            user.SecurityVersion++;
            user.UpdatedAt = DateTime.UtcNow;
            user.PasswordResetToken = null;
            user.PasswordResetTokenExpiresAt = null;

            await dbContext.SaveChangesAsync();

            var response = new ResetPasswordResponse(newPassword, DateTime.UtcNow);
            logger.LogInformation("用户密码已重置: {UserId}", userId);
            return ServiceResult<ResetPasswordResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "重置用户密码时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<ResetPasswordResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult> UpdateUserPlanAsync(Guid userId, UpdateUserPlanRequest request)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            user.PlanType = request.PlanType;
            user.PlanExpiresAt = request.PlanExpiresAt;
            user.UpdatedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();

            logger.LogInformation("用户套餐已更新: {UserId}, 新套餐: {PlanType}", userId, request.PlanType);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "更新用户套餐时发生错误，用户ID: {UserId}", userId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<AdminUserSessionListResponse>> GetUserSessionsAsync(Guid userId)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult<AdminUserSessionListResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            var sessions = await dbContext.UserSessions
                .Where(s => s.UserId == userId)
                .OrderByDescending(s => s.CreatedAt)
                .Select(s => new AdminUserSessionResponse(
                    s.Id,
                    s.UserId,
                    s.IpAddress,
                    s.UserAgent,
                    s.CreatedAt,
                    s.ExpiresAt,
                    s.ExpiresAt <= DateTime.UtcNow))
                .ToListAsync();

            var response = new AdminUserSessionListResponse(sessions, sessions.Count, 1, sessions.Count);
            return ServiceResult<AdminUserSessionListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户会话时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<AdminUserSessionListResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult> RevokeUserSessionsAsync(Guid userId)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            var sessions = await dbContext.UserSessions
                .Where(s => s.UserId == userId && s.ExpiresAt > DateTime.UtcNow)
                .ToListAsync();

            foreach (var session in sessions)
            {
                session.ExpiresAt = DateTime.UtcNow;
            }

            user.SecurityVersion++;
            user.UpdatedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();

            logger.LogInformation("用户所有会话已撤销: {UserId}, 撤销会话数: {SessionCount}", userId, sessions.Count);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "撤销用户会话时发生错误，用户ID: {UserId}", userId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<AdminUserTaskListResponse>> GetUserTasksAsync(Guid userId, int page, int pageSize)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult<AdminUserTaskListResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            var skip = (page - 1) * pageSize;

            var workerTasks = await dbContext.WorkerTasks
                .Where(t => t.UserId == userId)
                .OrderByDescending(t => t.CreatedAt)
                .Skip(skip)
                .Take(pageSize)
                .Select(t => new WorkerTaskResponse(
                    t.Id,
                    t.UserId,
                    t.BatchTaskId,
                    t.Name,
                    t.TaskType,
                    t.VideoId,
                    t.VideoTitle,
                    t.VideoUrl,
                    t.Status,
                    t.Progress,
                    t.Priority,
                    t.OutputFormat,
                    t.Quality,
                    t.StartTime,
                    t.EndTime,
                    t.ResultPath,
                    t.FileSize,
                    t.ErrorMessage,
                    t.CreatedAt,
                    t.StartedAt,
                    t.CompletedAt,
                    t.FileExpiresAt))
                .ToListAsync();

            var batchTasks = await dbContext.BatchTasks
                .Where(t => t.UserId == userId)
                .OrderByDescending(t => t.CreatedAt)
                .Skip(skip)
                .Take(pageSize)
                .Select(t => new BatchTaskResponse(
                    t.Id,
                    t.UserId,
                    t.Name,
                    t.SourceType,
                    t.SourceId,
                    t.SourceUrl,
                    t.SourceTitle,
                    t.TotalVideoCount,
                    t.SelectedVideoCount,
                    t.Status,
                    t.Progress,
                    t.CompletedTaskCount,
                    t.FailedTaskCount,
                    t.CreatedAt,
                    t.StartedAt,
                    t.CompletedAt,
                    new List<WorkerTaskResponse>()))
                .ToListAsync();

            var totalCount = await dbContext.WorkerTasks.CountAsync(t => t.UserId == userId) +
                           await dbContext.BatchTasks.CountAsync(t => t.UserId == userId);

            var response = new AdminUserTaskListResponse(workerTasks, batchTasks, totalCount, page, pageSize);
            return ServiceResult<AdminUserTaskListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户任务时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<AdminUserTaskListResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<AdminUserContentListResponse>> GetUserContentHistoryAsync(Guid userId, int page, int pageSize)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult<AdminUserContentListResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            var skip = (page - 1) * pageSize;

            var contentQuery = from task in dbContext.WorkerTasks
                             where task.UserId == userId && !string.IsNullOrEmpty(task.VideoId)
                             group task by task.VideoId into g
                             select new AdminUserContentResponse(
                                 g.Key,
                                 g.First().VideoTitle,
                                 g.First().VideoUrl,
                                 null,
                                 g.Count(),
                                 g.Min(t => t.CreatedAt),
                                 g.Max(t => t.CreatedAt));

            var totalCount = await contentQuery.CountAsync();
            var content = await contentQuery
                .OrderByDescending(c => c.LastTaskAt)
                .Skip(skip)
                .Take(pageSize)
                .ToListAsync();

            var response = new AdminUserContentListResponse(content, totalCount, page, pageSize);
            return ServiceResult<AdminUserContentListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户内容记录时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<AdminUserContentListResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<AdminUserBillingResponse>> GetUserBillingHistoryAsync(Guid userId)
    {
        try
        {
            var user = await dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return ServiceResult<AdminUserBillingResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));
            }

            var history = new List<AdminBillingHistoryResponse>();

            var response = new AdminUserBillingResponse(
                userId,
                user.PlanType,
                user.PlanExpiresAt,
                history);

            return ServiceResult<AdminUserBillingResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户计费记录时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<AdminUserBillingResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    private static string GenerateRandomPassword()
    {
        const string chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
        const string numbers = "23456789";
        const string upperCase = "ABCDEFGHJKLMNPQRSTUVWXYZ";
        const string lowerCase = "abcdefghijkmnpqrstuvwxyz";

        var password = new char[12];
        var random = new Random();

        password[0] = upperCase[random.Next(upperCase.Length)];
        password[1] = lowerCase[random.Next(lowerCase.Length)];
        password[2] = numbers[random.Next(numbers.Length)];

        for (int i = 3; i < password.Length; i++)
        {
            password[i] = chars[random.Next(chars.Length)];
        }

        for (int i = password.Length - 1; i > 0; i--)
        {
            int j = random.Next(i + 1);
            (password[i], password[j]) = (password[j], password[i]);
        }

        return new string(password);
    }
}
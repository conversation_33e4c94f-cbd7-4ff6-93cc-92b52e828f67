using System.Security.Claims;
using Api.Data;
using Api.Data.Models;
using Shared.Common;

namespace Api.Services;

public class UserService(AppDbContext dbContext, ILogger<UserService> logger)
{
    public async Task<ServiceResult<User>> GetOrCreateUserAsync(ClaimsPrincipal principal)
    {
        var userIdString = principal.FindFirstValue(ClaimTypes.NameIdentifier);
        if (!Guid.TryParse(userIdString, out var userId))
        {
            logger.LogError("ClaimsPrincipal 缺少有效的用户ID: {UserIdString}", userIdString);
            return ServiceResult<User>.Failure(new Error(ErrorType.Validation, ErrorKeys.INVALID_CREDENTIALS,
                DeveloperMessage: $"ClaimsPrincipal 缺少有效的用户ID: '{userIdString}'。"));
        }

        var user = await dbContext.Users.FindAsync(userId);
        if (user != null) return ServiceResult<User>.Success(user);

        var userTypeString = principal.FindFirstValue("UserType");
        if (Enum.TryParse<UserType>(userTypeString, out var userType) && userType == UserType.Anonymous)
        {
            var planTypeString = principal.FindFirstValue("PlanType");
            Enum.TryParse<UserPlanType>(planTypeString, out var planType);

            var newUser = new User
            {
                Id = userId,
                UserType = UserType.Anonymous,
                PlanType = planType,
                Status = UserAccountStatus.Active,
                LastActiveAt = DateTime.UtcNow
            };

            dbContext.Users.Add(newUser);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("匿名用户即时创建成功: {UserId}", userId);
            return ServiceResult<User>.Success(newUser);
        }

        logger.LogWarning("用户ID {UserId} 在数据库中未找到，且无法作为匿名用户创建。可能为无效会话。", userId);
        return ServiceResult<User>.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.INVALID_CREDENTIALS,
            DeveloperMessage: $"用户ID '{userId}' 在数据库中未找到，且无法作为匿名用户创建。可能为无效会话。"));
    }
}
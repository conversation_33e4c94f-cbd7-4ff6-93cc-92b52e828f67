'use client';

import {useState} from 'react';
import {useRouter} from 'next/navigation';
import {Badge} from "@/components/ui/badge";
import {Header} from "@/components/layout/Header";
import {Footer} from "@/components/layout/Footer";
import {UrlInputForm} from "@/components/features/url-input/UrlInputForm";
import {parseYouTubeUrl} from "@/lib/youtube";
import {ROUTES} from "@/lib/constants";
import {taskApi} from "@/lib/api";
import {toast} from "sonner";
import type {UrlSubmissionData} from "@/lib/types";

export default function Home() {
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter();

    const handleUrlSubmit = async (data: UrlSubmissionData) => {
        setIsLoading(true);

        try {
            if (data.type === 'single') {
                const url = data.urls[0];
                const linkInfo = parseYouTubeUrl(url);

                if (!linkInfo.isValid) {
                    toast.error('链接格式无效');
                    return;
                }

                switch (linkInfo.type) {
                    case 'video':
                        router.push(`${ROUTES.VIDEO_DOWNLOAD}?videoId=${linkInfo.id}`);
                        break;
                    case 'playlist':
                    case 'channel':
                        await createBatchTask([url]);
                        break;
                    default:
                        toast.error('不支持的链接类型');
                }
            } else {
                await createBatchTask(data.urls);
            }
        } catch (error) {
            console.error('处理链接时出错:', error);
            toast.error('处理链接时出现错误，请稍后重试');
        } finally {
            setIsLoading(false);
        }
    };

    const createBatchTask = async (urls: string[]) => {
        try {
            const response = await taskApi.createBatchTask({urls});

            if (response.success && response.data) {
                const taskData = response.data as any;
                const taskId = taskData.taskId || taskData.id || taskData.batchTaskId;

                if (taskId) {
                    router.push(`${ROUTES.BATCH_DOWNLOAD}?taskId=${taskId}`);
                    toast.success(`成功创建批量任务，开始处理 ${urls.length} 个链接`);
                } else {
                    throw new Error('未能获取任务ID');
                }
            } else {
                console.warn('API创建任务失败，使用备用方案:', response.error);

                const urlParams = new URLSearchParams();
                urls.forEach((url, index) => {
                    urlParams.append(`url${index}`, url);
                });

                router.push(`${ROUTES.BATCH_DOWNLOAD}?${urlParams.toString()}`);
                toast.success(`开始处理 ${urls.length} 个链接`);
            }
        } catch (error) {
            console.error('创建批量任务失败:', error);

            try {
                const urlParams = new URLSearchParams();
                urls.forEach((url, index) => {
                    urlParams.append(`url${index}`, url);
                });

                router.push(`${ROUTES.BATCH_DOWNLOAD}?${urlParams.toString()}`);
                toast.warning('无法连接服务器，将在本地处理链接');
            } catch (fallbackError) {
                toast.error('处理链接时出现错误，请稍后重试');
                throw error;
            }
        }
    };

    return (
        <div className="min-h-screen">
            <Header/>

            <section className="py-20 px-4">
                <div className="container mx-auto text-center max-w-4xl">
                    <Badge variant="secondary" className="mb-4">
                        免费 • 快速 • 安全
                    </Badge>

                    <h1 className="text-4xl md:text-6xl font-bold mb-6">
                        YouTube 视频下载
                    </h1>

                    <p className="text-xl text-muted-foreground mb-8">
                        支持单个视频和批量下载，提供多种格式选择，包括视频、音频、字幕和缩略图。
                    </p>

                    {/* 链接输入组件 */}
                    <UrlInputForm
                        onSubmit={handleUrlSubmit}
                        isLoading={isLoading}
                        className="mb-8"
                    />
                </div>
            </section>

            <Footer/>
        </div>
    );
}

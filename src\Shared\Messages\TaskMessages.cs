using Shared.Common;
using Shared.DTOs;

namespace Shared.Messages;

public interface ITaskMessage
{
    Guid TaskId { get; }
    Guid UserId { get; }
    WorkerTaskType TaskType { get; }
    WorkerTaskPriority Priority { get; }
    string? VideoId { get; }
    string? VideoUrl { get; }
    string? OutputFormat { get; }
    string? Quality { get; }
    int? StartTime { get; }
    int? EndTime { get; }
    DateTime CreatedAt { get; }
}

public record TaskMessage(
    Guid TaskId,
    Guid UserId,
    WorkerTaskType TaskType,
    WorkerTaskPriority Priority,
    string? VideoId,
    string? VideoUrl,
    string? OutputFormat,
    string? Quality,
    int? StartTime,
    int? EndTime,
    DateTime CreatedAt) : ITaskMessage;

public interface ITaskProgressMessage
{
    Guid TaskId { get; }
    int Progress { get; }
    string? CurrentStep { get; }
    int? EstimatedTimeRemaining { get; }
    Guid? WorkerNodeId { get; }
    DateTime UpdatedAt { get; }
}

public record TaskProgressMessage(Guid TaskId, int Progress, string? CurrentStep, int? EstimatedTimeRemaining, Guid? WorkerNodeId, DateTime UpdatedAt)
    : ITaskProgressMessage;

public interface ITaskResultMessage
{
    Guid TaskId { get; }
    WorkerTaskStatus Status { get; }
    int Progress { get; }
    string? ErrorMessage { get; }
    string? OutputFilePath { get; }
    long? FileSize { get; }
    long? ProcessingTime { get; }
    Guid? WorkerNodeId { get; }
    DateTime CompletedAt { get; }
}

public record TaskResultMessage(
    Guid TaskId,
    WorkerTaskStatus Status,
    int Progress,
    string? ErrorMessage,
    string? OutputFilePath,
    long? FileSize,
    long? ProcessingTime,
    Guid? WorkerNodeId,
    DateTime CompletedAt) : ITaskResultMessage;

public record BatchTaskMessage(Guid BatchTaskId, Guid UserId, List<Guid> TaskIds, WorkerTaskPriority Priority, DateTime CreatedAt);

public record TaskCancellationMessage(Guid TaskId, Guid UserId, string? Reason, DateTime CancelledAt);

public record TaskAssignmentMessage(Guid TaskId, Guid WorkerNodeId, DateTime AssignedAt, WorkerTaskPriority Priority);

public record TaskProcessRequest(Guid TaskId, string VideoId, string? OutputFormat, string? Quality, int? StartTime, int? EndTime, ProxyInfo? Proxy);

public record TaskProgressInfo(int Progress, string CurrentStep);

public record TaskProcessResult(string FilePath, long FileSize, TimeSpan Duration, string Format);
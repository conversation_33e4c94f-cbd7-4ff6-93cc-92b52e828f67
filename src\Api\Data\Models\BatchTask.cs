﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.Common;

namespace Api.Data.Models;

public class BatchTask
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public required Guid UserId { get; set; }
    public required string Name { get; set; }
    public required BatchTaskType SourceType { get; set; }
    public required string SourceId { get; set; }
    public required string SourceUrl { get; set; }
    public string? SourceTitle { get; set; }
    public string? SourceDescription { get; set; }
    public string? SourceThumbnail { get; set; }
    public int TotalVideoCount { get; set; }
    public int SelectedVideoCount { get; set; }
    public required string Configuration { get; set; }
    public BatchTaskStatus Status { get; set; } = BatchTaskStatus.Created;
    public int Progress { get; set; }
    public int CompletedTaskCount { get; set; }
    public int FailedTaskCount { get; set; }
    public int CancelledTaskCount { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime CreatedAt { get; init; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public User User { get; set; } = null!;
    public ICollection<WorkerTask> WorkerTasks { get; set; } = [];
}

public class BatchTaskConfiguration : IEntityTypeConfiguration<BatchTask>
{
    public void Configure(EntityTypeBuilder<BatchTask> builder)
    {
        builder.ToTable("batch_tasks", "public");
        builder.HasKey(bt => bt.Id);
        builder.Property(bt => bt.Id).ValueGeneratedNever();
        builder.Property(bt => bt.UserId).IsRequired().HasColumnName("user_id");
        builder.Property(bt => bt.Name).IsRequired().HasMaxLength(255).HasColumnName("name");
        builder.Property(bt => bt.SourceType).IsRequired().HasColumnName("source_type").HasConversion<string>();
        builder.Property(bt => bt.SourceId).IsRequired().HasMaxLength(255).HasColumnName("source_id");
        builder.Property(bt => bt.SourceUrl).IsRequired().HasMaxLength(500).HasColumnName("source_url");
        builder.Property(bt => bt.SourceTitle).HasMaxLength(500).HasColumnName("source_title");
        builder.Property(bt => bt.SourceDescription).HasMaxLength(2000).HasColumnName("source_description");
        builder.Property(bt => bt.SourceThumbnail).HasMaxLength(500).HasColumnName("source_thumbnail");
        builder.Property(bt => bt.TotalVideoCount).HasColumnName("total_video_count").HasDefaultValue(0);
        builder.Property(bt => bt.SelectedVideoCount).HasColumnName("selected_video_count").HasDefaultValue(0);
        builder.Property(bt => bt.Configuration).IsRequired().HasColumnType("jsonb").HasColumnName("configuration");
        builder.Property(bt => bt.Status).IsRequired().HasColumnName("status").HasConversion<string>();
        builder.Property(bt => bt.Progress).HasColumnName("progress").HasDefaultValue(0);
        builder.Property(bt => bt.CompletedTaskCount).HasColumnName("completed_task_count").HasDefaultValue(0);
        builder.Property(bt => bt.FailedTaskCount).HasColumnName("failed_task_count").HasDefaultValue(0);
        builder.Property(bt => bt.CancelledTaskCount).HasColumnName("cancelled_task_count").HasDefaultValue(0);
        builder.Property(bt => bt.ErrorMessage).HasMaxLength(1000).HasColumnName("error_message");
        builder.Property(bt => bt.CreatedAt).IsRequired().HasColumnName("created_at").ValueGeneratedOnAdd();
        builder.Property(bt => bt.StartedAt).HasColumnName("started_at");
        builder.Property(bt => bt.CompletedAt).HasColumnName("completed_at");
        builder.Property(bt => bt.UpdatedAt).IsRequired().HasColumnName("updated_at").ValueGeneratedOnAddOrUpdate();

        builder.HasIndex(bt => bt.UserId).HasDatabaseName("ix_batch_tasks_user_id");
        builder.HasIndex(bt => bt.SourceType).HasDatabaseName("ix_batch_tasks_source_type");
        builder.HasIndex(bt => bt.Status).HasDatabaseName("ix_batch_tasks_status");
        builder.HasIndex(bt => bt.CreatedAt).HasDatabaseName("ix_batch_tasks_created_at");
        builder.HasIndex(bt => new { bt.UserId, bt.Status, bt.CreatedAt }).HasDatabaseName("ix_batch_tasks_user_status_created");
        builder.HasIndex(bt => new { bt.Status, bt.Progress }).HasDatabaseName("ix_batch_tasks_status_progress");

        builder.HasOne(bt => bt.User).WithMany(u => u.BatchTasks).HasForeignKey(bt => bt.UserId).OnDelete(DeleteBehavior.Cascade);
    }
}
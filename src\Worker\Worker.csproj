<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CliWrap" Version="3.9.0"/>
        <PackageReference Include="MassTransit.RabbitMQ" Version="8.5.0"/>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5"/>
        <PackageReference Include="Scalar.AspNetCore" Version="2.5.0"/>
        <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="9.0.6"/>
        <PackageReference Include="System.Management" Version="9.0.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Shared\Shared.csproj"/>
    </ItemGroup>

</Project>

using System.Net.NetworkInformation;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Worker.Scheduled;

namespace Worker.Services;

/// <summary>
///     健康检查服务
/// </summary>
public class HealthCheckService
{
    private readonly IConfiguration _configuration;
    private readonly HeartbeatService _heartbeatService;
    private readonly ILogger<HealthCheckService> _logger;
    private readonly MetricsService _metricsService;
    private readonly YouTubeService _youTubeService;
    private readonly YtDlpService _ytDlpService;

    public HealthCheckService(IConfiguration configuration, ILogger<HealthCheckService> logger, MetricsService metricsService,
        HeartbeatService heartbeatService, YouTubeService youTubeService, YtDlpService ytDlpService)
    {
        _configuration = configuration;
        _logger = logger;
        _metricsService = metricsService;
        _heartbeatService = heartbeatService;
        _youTubeService = youTubeService;
        _ytDlpService = ytDlpService;
    }

    /// <summary>
    ///     执行完整的健康检查
    /// </summary>
    public async Task<HealthCheckResult> PerformHealthCheckAsync()
    {
        var startTime = DateTime.UtcNow;
        var checks = new List<ComponentHealthCheck>();

        try
        {
            // 1. 系统资源检查
            checks.Add(await CheckSystemResourcesAsync());

            // 2. 节点注册状态检查
            checks.Add(CheckNodeRegistration());

            // 3. 外部依赖检查
            checks.Add(await CheckExternalDependenciesAsync());

            // 4. YouTube API提供商检查
            checks.Add(await CheckYouTubeProvidersAsync());

            // 5. yt-dlp可用性检查
            checks.Add(await CheckYtDlpAvailabilityAsync());

            // 6. 文件系统检查
            checks.Add(await CheckFileSystemAsync());

            // 7. 网络连接检查
            checks.Add(await CheckNetworkConnectivityAsync());

            var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;
            var overallStatus = DetermineOverallStatus(checks);

            return new HealthCheckResult
            {
                Status = overallStatus,
                CheckedAt = DateTime.UtcNow,
                Duration = TimeSpan.FromMilliseconds(duration),
                ComponentChecks = checks,
                NodeId = _heartbeatService.GetNodeId(),
                NodeName = Environment.MachineName,
                Version = "1.0.0"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "健康检查执行失败");
            return new HealthCheckResult
            {
                Status = HealthStatus.Critical,
                CheckedAt = DateTime.UtcNow,
                Duration = TimeSpan.FromMilliseconds((DateTime.UtcNow - startTime).TotalMilliseconds),
                ComponentChecks = checks,
                ErrorMessage = $"健康检查执行失败: {ex.Message}",
                NodeId = _heartbeatService.GetNodeId(),
                NodeName = Environment.MachineName,
                Version = "1.0.0"
            };
        }
    }

    private async Task<ComponentHealthCheck> CheckSystemResourcesAsync()
    {
        try
        {
            var metrics = await _metricsService.GetSystemMetricsAsync();
            var status = HealthStatus.Healthy;
            var details = new Dictionary<string, object>();

            // CPU检查
            if (metrics.CpuUsagePercent > 90)
                status = HealthStatus.Critical;
            else if (metrics.CpuUsagePercent > 70)
                status = HealthStatus.Warning;

            // 内存检查
            if (metrics.MemoryUsagePercent > 90)
                status = HealthStatus.Critical;
            else if (metrics.MemoryUsagePercent > 70 && status == HealthStatus.Healthy)
                status = HealthStatus.Warning;

            // 磁盘检查
            if (metrics.DiskUsagePercent > 95)
                status = HealthStatus.Critical;
            else if (metrics.DiskUsagePercent > 85 && status == HealthStatus.Healthy)
                status = HealthStatus.Warning;

            details["cpu_usage_percent"] = metrics.CpuUsagePercent;
            details["memory_usage_percent"] = metrics.MemoryUsagePercent;
            details["disk_usage_percent"] = metrics.DiskUsagePercent;
            details["active_tasks"] = metrics.ProcessingTasks;

            return new ComponentHealthCheck
            {
                Component = "SystemResources",
                Status = status,
                Details = details,
                Message = status == HealthStatus.Healthy ? "系统资源正常" : "系统资源使用率过高"
            };
        }
        catch (Exception ex)
        {
            return new ComponentHealthCheck
            {
                Component = "SystemResources",
                Status = HealthStatus.Critical,
                Message = $"系统资源检查失败: {ex.Message}"
            };
        }
    }

    private ComponentHealthCheck CheckNodeRegistration()
    {
        var isRegistered = _heartbeatService.IsRegistered;
        var nodeId = _heartbeatService.GetNodeId();

        return new ComponentHealthCheck
        {
            Component = "NodeRegistration",
            Status = isRegistered ? HealthStatus.Healthy : HealthStatus.Critical,
            Message = isRegistered ? "节点已注册" : "节点未注册",
            Details = new Dictionary<string, object>
            {
                ["is_registered"] = isRegistered,
                ["node_id"] = nodeId?.ToString() ?? "null"
            }
        };
    }

    private async Task<ComponentHealthCheck> CheckExternalDependenciesAsync()
    {
        try
        {
            var coreServiceUrl = _configuration["CoreService:BaseUrl"];
            if (string.IsNullOrEmpty(coreServiceUrl))
                return new ComponentHealthCheck
                {
                    Component = "ExternalDependencies",
                    Status = HealthStatus.Critical,
                    Message = "核心服务URL未配置"
                };

            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(10);

            var response = await httpClient.GetAsync($"{coreServiceUrl}/health");
            var status = response.IsSuccessStatusCode ? HealthStatus.Healthy : HealthStatus.Critical;

            return new ComponentHealthCheck
            {
                Component = "ExternalDependencies",
                Status = status,
                Message = status == HealthStatus.Healthy ? "外部依赖正常" : "核心服务不可访问",
                Details = new Dictionary<string, object>
                {
                    ["core_service_url"] = coreServiceUrl,
                    ["response_status"] = response.StatusCode.ToString()
                }
            };
        }
        catch (Exception ex)
        {
            return new ComponentHealthCheck
            {
                Component = "ExternalDependencies",
                Status = HealthStatus.Critical,
                Message = $"外部依赖检查失败: {ex.Message}"
            };
        }
    }

    private async Task<ComponentHealthCheck> CheckYouTubeProvidersAsync()
    {
        try
        {
            var providersHealth = await _youTubeService.GetProvidersHealthAsync();
            var providersInfo = _youTubeService.GetProvidersInfo();

            var availableCount = providersHealth.Count(p => p.Value);
            var status = availableCount > 0 ? HealthStatus.Healthy : HealthStatus.Warning;

            return new ComponentHealthCheck
            {
                Component = "YouTubeProviders",
                Status = status,
                Message = $"可用提供商: {availableCount}",
                Details = new Dictionary<string, object>
                {
                    ["available_providers"] = availableCount,
                    ["providers_health"] = providersHealth
                }
            };
        }
        catch (Exception ex)
        {
            return new ComponentHealthCheck
            {
                Component = "YouTubeProviders",
                Status = HealthStatus.Critical,
                Message = $"YouTube提供商检查失败: {ex.Message}"
            };
        }
    }

    private async Task<ComponentHealthCheck> CheckYtDlpAvailabilityAsync()
    {
        try
        {
            var isAvailable = await _ytDlpService.IsYtDlpAvailableAsync();

            return new ComponentHealthCheck
            {
                Component = "YtDlpAvailability",
                Status = isAvailable ? HealthStatus.Healthy : HealthStatus.Critical,
                Message = isAvailable ? "yt-dlp可用" : "yt-dlp不可用",
                Details = new Dictionary<string, object>
                {
                    ["is_available"] = isAvailable
                }
            };
        }
        catch (Exception ex)
        {
            return new ComponentHealthCheck
            {
                Component = "YtDlpAvailability",
                Status = HealthStatus.Critical,
                Message = $"yt-dlp检查失败: {ex.Message}"
            };
        }
    }

    private async Task<ComponentHealthCheck> CheckFileSystemAsync()
    {
        try
        {
            var downloadPath = _configuration["Worker:DownloadPath"] ?? Path.GetTempPath();
            var testFile = Path.Combine(downloadPath, $"health_check_{Guid.NewGuid()}.tmp");

            // 测试写入权限
            await File.WriteAllTextAsync(testFile, "health check");
            File.Delete(testFile);

            return new ComponentHealthCheck
            {
                Component = "FileSystem",
                Status = HealthStatus.Healthy,
                Message = "文件系统正常",
                Details = new Dictionary<string, object>
                {
                    ["download_path"] = downloadPath,
                    ["write_permission"] = true
                }
            };
        }
        catch (Exception ex)
        {
            return new ComponentHealthCheck
            {
                Component = "FileSystem",
                Status = HealthStatus.Critical,
                Message = $"文件系统检查失败: {ex.Message}"
            };
        }
    }

    private Task<ComponentHealthCheck> CheckNetworkConnectivityAsync()
    {
        try
        {
            // 简化网络连接检查，使用基本的网络可用性检查
            var isConnected = NetworkInterface.GetIsNetworkAvailable();

            return Task.FromResult(new ComponentHealthCheck
            {
                Component = "NetworkConnectivity",
                Status = isConnected ? HealthStatus.Healthy : HealthStatus.Warning,
                Message = isConnected ? "网络连接正常" : "网络连接异常",
                Details = new Dictionary<string, object>
                {
                    ["is_connected"] = isConnected
                }
            });
        }
        catch (Exception ex)
        {
            return Task.FromResult(new ComponentHealthCheck
            {
                Component = "NetworkConnectivity",
                Status = HealthStatus.Critical,
                Message = $"网络连接检查失败: {ex.Message}"
            });
        }
    }

    private static HealthStatus DetermineOverallStatus(List<ComponentHealthCheck> checks)
    {
        if (checks.Any(c => c.Status == HealthStatus.Critical))
            return HealthStatus.Critical;

        if (checks.Any(c => c.Status == HealthStatus.Warning))
            return HealthStatus.Warning;

        return HealthStatus.Healthy;
    }
}
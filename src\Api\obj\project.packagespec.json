﻿"restore":{"projectUniqueName":"D:\\学习\\YTDownloader\\src\\Api\\Api.csproj","projectName":"Api","projectPath":"D:\\学习\\YTDownloader\\src\\Api\\Api.csproj","outputPath":"D:\\学习\\YTDownloader\\src\\Api\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\学习\\YTDownloader\\src\\Shared\\Shared.csproj":{"projectPath":"D:\\学习\\YTDownloader\\src\\Shared\\Shared.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"BCrypt.Net-Next":{"target":"Package","version":"[4.0.3, )"},"FluentValidation.AspNetCore":{"target":"Package","version":"[11.3.1, )"},"MassTransit.RabbitMQ":{"target":"Package","version":"[8.5.0, )"},"Microsoft.AspNetCore.Authentication.Cookies":{"target":"Package","version":"[2.3.0, )"},"Microsoft.AspNetCore.OpenApi":{"target":"Package","version":"[9.0.5, )"},"Microsoft.EntityFrameworkCore.Design":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[9.0.6, )"},"Npgsql.EntityFrameworkCore.PostgreSQL":{"target":"Package","version":"[9.0.4, )"},"Scalar.AspNetCore":{"target":"Package","version":"[2.4.22, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}
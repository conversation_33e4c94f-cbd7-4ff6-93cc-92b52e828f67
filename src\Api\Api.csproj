<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3"/>
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1"/>
        <PackageReference Include="MassTransit.RabbitMQ" Version="8.5.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Cookies" Version="2.3.0"/>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4"/>
        <PackageReference Include="Scalar.AspNetCore" Version="2.4.22"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Shared\Shared.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Data\Migrations\"/>
        <Folder Include="Helpers\"/>
    </ItemGroup>

</Project>

using Api.Data;
using Api.Services;
using Microsoft.EntityFrameworkCore;
using Shared.Common;

namespace Api.Scheduled;

public class CleanupService : BackgroundService
{
    private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(1);
    private readonly ILogger<CleanupService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public CleanupService(IServiceProvider serviceProvider, ILogger<CleanupService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("清理服务已启动");

        while (!stoppingToken.IsCancellationRequested)
            try
            {
                await PerformCleanupAsync();
                await Task.Delay(_cleanupInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理操作时发生错误");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }

        _logger.LogInformation("清理服务已停止");
    }

    private async Task PerformCleanupAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var authService = scope.ServiceProvider.GetRequiredService<AuthService>();

        var now = DateTime.UtcNow;

        var expiredSessionsCount = await authService.CleanupExpiredSessionsAsync();
        if (expiredSessionsCount > 0)
            _logger.LogInformation("已清理 {Count} 个过期会话", expiredSessionsCount);

        var expiredEmailTokensCount = await dbContext.Users.Where(u => u.EmailVerificationTokenExpiresAt <= now && u.EmailVerificationToken != null)
            .ExecuteUpdateAsync(u => u
                .SetProperty(x => x.EmailVerificationToken, (string?)null).SetProperty(x => x.EmailVerificationTokenExpiresAt, (DateTime?)null)
                .SetProperty(x => x.UpdatedAt, now));

        if (expiredEmailTokensCount > 0)
            _logger.LogInformation("已清理 {Count} 个过期邮箱验证令牌", expiredEmailTokensCount);

        var expiredPasswordTokensCount = await dbContext.Users.Where(u => u.PasswordResetTokenExpiresAt <= now && u.PasswordResetToken != null)
            .ExecuteUpdateAsync(u => u
                .SetProperty(x => x.PasswordResetToken, (string?)null).SetProperty(x => x.PasswordResetTokenExpiresAt, (DateTime?)null)
                .SetProperty(x => x.UpdatedAt, now));

        if (expiredPasswordTokensCount > 0)
            _logger.LogInformation("已清理 {Count} 个过期密码重置令牌", expiredPasswordTokensCount);

        var inactiveThreshold = now.AddDays(-180);
        var inactiveAnonymousUsersCount = await dbContext.Users
            .Where(u => u.UserType == UserType.Anonymous && u.LastActiveAt < inactiveThreshold).ExecuteDeleteAsync();

        if (inactiveAnonymousUsersCount > 0)
            _logger.LogInformation("已清理 {Count} 个不活跃匿名用户", inactiveAnonymousUsersCount);

        var fileExpiryThreshold = now.AddDays(-3);
        var expiredTasksCount = await dbContext.WorkerTasks
            .Where(t => t.Status == WorkerTaskStatus.Completed && t.CompletedAt.HasValue && t.CompletedAt.Value < fileExpiryThreshold &&
                        !string.IsNullOrEmpty(t.ResultPath)).ExecuteUpdateAsync(t =>
                t.SetProperty(x => x.ResultPath, (string?)null).SetProperty(x => x.ResultFileSize, (long?)null).SetProperty(x => x.FileExpiresAt, now)
                    .SetProperty(x => x.UpdatedAt, now));

        if (expiredTasksCount > 0)
            _logger.LogInformation("已清理 {Count} 个过期任务文件", expiredTasksCount);

        var workerResetThreshold = now.AddDays(-7);
        var resetWorkerFailuresCount = await dbContext.Workers.Where(w => w.LastActiveAt < workerResetThreshold && w.FailureCount > 0)
            .ExecuteUpdateAsync(w => w.SetProperty(x => x.FailureCount, 0).SetProperty(x => x.UpdatedAt, now));

        if (resetWorkerFailuresCount > 0)
            _logger.LogInformation("已重置 {Count} 个不活跃工作节点的失败计数", resetWorkerFailuresCount);

        var totalCleaned = expiredSessionsCount + expiredEmailTokensCount + expiredPasswordTokensCount + inactiveAnonymousUsersCount + expiredTasksCount +
                           resetWorkerFailuresCount;
        if (totalCleaned > 0)
            _logger.LogInformation("清理完成。总共清理项目: {Count}", totalCleaned);
    }
}
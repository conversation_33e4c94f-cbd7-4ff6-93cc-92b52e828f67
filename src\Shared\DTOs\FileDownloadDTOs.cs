using FluentValidation;

namespace Shared.DTOs;

public record BatchDownloadRequest(List<Guid> TaskIds);

public class BatchDownloadRequestValidator : AbstractValidator<BatchDownloadRequest>
{
    public BatchDownloadRequestValidator()
    {
        RuleFor(x => x.TaskIds).NotEmpty().WithMessage("任务ID列表不能为空").Must(ids => ids.Count <= 50).WithMessage("一次最多只能处理50个任务")
            .Must(ids => ids.All(id => id != Guid.Empty)).WithMessage("任务ID不能为空");
    }
}

public record BatchDownloadResponse(List<BatchDownloadResult> Results, int SuccessCount, int FailureCount);

public record BatchDownloadResult(Guid TaskId, bool Success, string? DownloadUrl, string? ErrorMessage);

public record FileDownloadHistoryItem(
    Guid TaskId,
    string TaskName,
    string? VideoId,
    string? VideoTitle,
    string FileName,
    long FileSize,
    DateTime CompletedAt,
    bool IsExpired);

public record FileInfoResponse(Guid TaskId, bool HasFile, string? DownloadUrl, string? FileName, long? FileSize, string? ContentType, DateTime? ExpiresAt);

public record FileDownloadInfo(Guid TaskId, Guid UserId, string FilePath, string FileName, long FileSize, string ContentType);

public record DownloadHistoryRequest(int Page = 1, int PageSize = 20, string? FileType = null, DateTime? StartDate = null, DateTime? EndDate = null);

public class DownloadHistoryRequestValidator : AbstractValidator<DownloadHistoryRequest>
{
    public DownloadHistoryRequestValidator()
    {
        RuleFor(x => x.Page).GreaterThan(0).WithMessage("页码必须大于0");

        RuleFor(x => x.PageSize).InclusiveBetween(1, 100).WithMessage("每页大小必须在1-100之间");

        RuleFor(x => x.FileType).Must(type => type == null || new[] { "video", "audio", "subtitle", "thumbnail" }.Contains(type)).WithMessage("文件类型无效");
    }
}

public record FileStatsResponse(
    int TotalFiles,
    long TotalSize,
    int VideoFiles,
    int AudioFiles,
    int SubtitleFiles,
    int ThumbnailFiles,
    int ExpiredFiles,
    DateTime LastDownload);

public record FileCleanupRequest(bool CleanExpiredFiles = true, bool CleanFailedTasks = false, DateTime? OlderThan = null);

public class FileCleanupRequestValidator : AbstractValidator<FileCleanupRequest>
{
    public FileCleanupRequestValidator()
    {
        RuleFor(x => x.OlderThan).LessThan(DateTime.UtcNow).When(x => x.OlderThan.HasValue).WithMessage("清理时间不能是未来时间");
    }
}

public record FileCleanupResponse(int CleanedFiles, long FreedSpace, List<string> CleanedFileTypes);

public record DownloadLinkRequest(Guid TaskId, TimeSpan? ValidFor = null);

public class DownloadLinkRequestValidator : AbstractValidator<DownloadLinkRequest>
{
    public DownloadLinkRequestValidator()
    {
        RuleFor(x => x.TaskId).NotEmpty().WithMessage("任务ID不能为空");

        RuleFor(x => x.ValidFor).Must(duration => duration == null || (duration.Value.TotalMinutes >= 1 && duration.Value.TotalHours <= 24))
            .WithMessage("有效期必须在1分钟到24小时之间");
    }
}

public record DownloadLinkResponse(string DownloadUrl, DateTime ExpiresAt, string FileName, long FileSize, string ContentType);
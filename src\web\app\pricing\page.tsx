import {Metadata} from 'next'
import {Badge} from '@/components/ui/badge'
import {Button} from '@/components/ui/button'
import {Header} from '@/components/layout/Header'
import {Footer} from '@/components/layout/Footer'

export const metadata: Metadata = {
    title: 'Pricing | YouTube Downloader',
    description: 'Choose the perfect plan for your YouTube downloading needs. Free and premium options available.',
}

export default function PricingPage() {
    return (
        <div className="min-h-screen">
            <Header/>

            <div className="container mx-auto px-4 py-8 max-w-6xl">
                <div className="space-y-12">
                    <div className="text-center space-y-4">
                        <h1 className="text-4xl font-bold">价格方案</h1>
                        <p className="text-xl text-muted-foreground">
                            选择适合您需求的下载方案
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="border rounded-lg p-6 space-y-6">
                            <div className="text-center space-y-2">
                                <h3 className="text-2xl font-bold">免费版</h3>
                                <div className="text-3xl font-bold">¥0</div>
                                <p className="text-muted-foreground">永久免费</p>
                            </div>

                            <ul className="space-y-3">
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>单个视频下载</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>最高720p分辨率</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>音频下载</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>字幕下载</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-muted-foreground">✗</span>
                                    <span className="text-muted-foreground">批量下载</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-muted-foreground">✗</span>
                                    <span className="text-muted-foreground">高清下载</span>
                                </li>
                            </ul>

                            <Button className="w-full" variant="outline">
                                开始使用
                            </Button>
                        </div>

                        <div className="border rounded-lg p-6 space-y-6 relative">
                            <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                推荐
                            </Badge>

                            <div className="text-center space-y-2">
                                <h3 className="text-2xl font-bold">高级版</h3>
                                <div className="text-3xl font-bold">¥29</div>
                                <p className="text-muted-foreground">每月</p>
                            </div>

                            <ul className="space-y-3">
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>所有免费功能</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>最高4K分辨率</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>批量下载</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>播放列表下载</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>无广告体验</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>优先处理</span>
                                </li>
                            </ul>

                            <Button className="w-full">
                                立即升级
                            </Button>
                        </div>

                        <div className="border rounded-lg p-6 space-y-6">
                            <div className="text-center space-y-2">
                                <h3 className="text-2xl font-bold">专业版</h3>
                                <div className="text-3xl font-bold">¥99</div>
                                <p className="text-muted-foreground">每月</p>
                            </div>

                            <ul className="space-y-3">
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>所有高级功能</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>最高8K分辨率</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>API访问</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>自定义格式</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>专属客服</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <span className="text-green-500">✓</span>
                                    <span>商业使用许可</span>
                                </li>
                            </ul>

                            <Button className="w-full" variant="outline">
                                联系销售
                            </Button>
                        </div>
                    </div>

                    <div className="text-center space-y-4">
                        <h2 className="text-2xl font-bold">常见问题</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                            <div className="space-y-2">
                                <h3 className="font-semibold">可以随时取消订阅吗？</h3>
                                <p className="text-muted-foreground text-sm">
                                    是的，您可以随时取消订阅，取消后将在当前计费周期结束时生效。
                                </p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="font-semibold">支持哪些支付方式？</h3>
                                <p className="text-muted-foreground text-sm">
                                    我们支持支付宝、微信支付、银行卡等多种支付方式。
                                </p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="font-semibold">有免费试用吗？</h3>
                                <p className="text-muted-foreground text-sm">
                                    高级版提供7天免费试用，专业版提供3天免费试用。
                                </p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="font-semibold">可以升级或降级吗？</h3>
                                <p className="text-muted-foreground text-sm">
                                    可以随时升级或降级您的订阅计划，费用将按比例调整。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Footer/>
        </div>
    )
}

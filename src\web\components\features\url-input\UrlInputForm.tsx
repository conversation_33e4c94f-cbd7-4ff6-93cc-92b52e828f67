'use client';

import {useCallback, useRef, useState} from 'react';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Textarea} from '@/components/ui/textarea';
import {Card, CardContent} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from '@/components/ui/collapsible';
import {AlertCircle, CheckCircle, ChevronDown, ChevronUp, FileText, Loader2, Search, Upload, X} from 'lucide-react';
import {validateMultipleYouTubeUrls, validateYouTubeUrl} from '@/lib/youtube';
import {YOUTUBE_CONSTANTS} from '@/lib/constants';
import {cn} from '@/lib/utils';

interface UrlInputFormProps {
    onSubmit: (data: { type: 'single' | 'multiple'; urls: string[]; videoIds?: string[] }) => void;
    isLoading?: boolean;
    className?: string;
}

export function UrlInputForm({onSubmit, isLoading = false, className}: UrlInputFormProps) {
    const [singleUrl, setSingleUrl] = useState('');
    const [multipleUrls, setMultipleUrls] = useState('');
    const [isMultipleExpanded, setIsMultipleExpanded] = useState(false);
    const [singleUrlValidation, setSingleUrlValidation] = useState<{ isValid: boolean; error?: string } | null>(null);
    const [multipleUrlsValidation, setMultipleUrlsValidation] = useState<{
        validCount: number;
        totalCount: number;
        invalidUrls: string[];
    } | null>(null);
    const [dragActive, setDragActive] = useState(false);

    const fileInputRef = useRef<HTMLInputElement>(null);

    // 单链接验证
    const validateSingleUrl = useCallback((url: string) => {
        if (!url.trim()) {
            setSingleUrlValidation(null);
            return;
        }

        const validation = validateYouTubeUrl(url);
        setSingleUrlValidation({
            isValid: validation.isValid,
            error: validation.error
        });
    }, []);

    // 多链接验证
    const validateMultipleUrlsInput = useCallback((text: string) => {
        if (!text.trim()) {
            setMultipleUrlsValidation(null);
            return;
        }

        const urls = text.split('\n').filter(url => url.trim());
        const validation = validateMultipleYouTubeUrls(urls);

        setMultipleUrlsValidation({
            validCount: validation.validCount,
            totalCount: validation.totalCount,
            invalidUrls: validation.invalidUrls
        });
    }, []);

    // 处理单链接输入
    const handleSingleUrlChange = (value: string) => {
        setSingleUrl(value);
        validateSingleUrl(value);
    };

    // 处理多链接输入
    const handleMultipleUrlsChange = (value: string) => {
        setMultipleUrls(value);
        validateMultipleUrlsInput(value);
    };

    // 清空单链接
    const clearSingleUrl = () => {
        setSingleUrl('');
        setSingleUrlValidation(null);
    };

    // 清空多链接
    const clearMultipleUrls = () => {
        setMultipleUrls('');
        setMultipleUrlsValidation(null);
    };

    // 文件拖拽处理
    const handleDrag = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else if (e.type === 'dragleave') {
            setDragActive(false);
        }
    }, []);

    // 文件放置处理
    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        const files = Array.from(e.dataTransfer.files);
        const txtFile = files.find(file => file.type === 'text/plain' || file.name.endsWith('.txt'));

        if (txtFile) {
            readFile(txtFile);
        }
    }, []);

    // 读取文件内容
    const readFile = (file: File) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target?.result as string;
            if (content) {
                setMultipleUrls(content);
                validateMultipleUrlsInput(content);
                if (!isMultipleExpanded) {
                    setIsMultipleExpanded(true);
                }
            }
        };
        reader.readAsText(file);
    };

    // 文件选择处理
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            readFile(file);
        }
    };

    // 判断是否可以提交
    const canSubmit = () => {
        if (isLoading) return false;

        // 单链接模式
        if (!isMultipleExpanded) {
            return singleUrl.trim() && singleUrlValidation?.isValid;
        }

        // 多链接模式
        return multipleUrlsValidation &&
            multipleUrlsValidation.validCount >= YOUTUBE_CONSTANTS.MIN_MULTI_VIDEOS &&
            multipleUrlsValidation.validCount <= YOUTUBE_CONSTANTS.MAX_MULTI_VIDEOS;
    };

    // 提交处理
    const handleSubmit = () => {
        if (!canSubmit()) return;

        if (!isMultipleExpanded) {
            // 单链接提交
            onSubmit({
                type: 'single',
                urls: [singleUrl.trim()]
            });
        } else {
            // 多链接提交
            const urls = multipleUrls.split('\n')
                .map(url => url.trim())
                .filter(url => url && validateYouTubeUrl(url).isValid);

            onSubmit({
                type: 'multiple',
                urls
            });
        }
    };

    return (
        <Card className={cn('w-full max-w-4xl mx-auto', className)}>
            <CardContent className="p-6 space-y-6">
                {/* 单链接输入区域 */}
                <div className="space-y-2">
                    <div className="relative">
                        <Input
                            placeholder="粘贴YouTube视频、播放列表或频道链接..."
                            value={singleUrl}
                            onChange={(e) => handleSingleUrlChange(e.target.value)}
                            disabled={isLoading}
                            className={cn(
                                'pr-10',
                                singleUrlValidation?.isValid === false && 'border-red-500',
                                singleUrlValidation?.isValid === true && 'border-green-500'
                            )}
                        />
                        {singleUrl && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                                onClick={clearSingleUrl}
                                disabled={isLoading}
                            >
                                <X className="h-4 w-4"/>
                            </Button>
                        )}
                    </div>

                    {/* 单链接验证状态 */}
                    {singleUrlValidation && (
                        <div className="flex items-center gap-2 text-sm">
                            {singleUrlValidation.isValid ? (
                                <>
                                    <CheckCircle className="h-4 w-4 text-green-500"/>
                                    <span className="text-green-600">链接格式正确</span>
                                </>
                            ) : (
                                <>
                                    <AlertCircle className="h-4 w-4 text-red-500"/>
                                    <span className="text-red-600">{singleUrlValidation.error}</span>
                                </>
                            )}
                        </div>
                    )}
                </div>

                {/* 多链接输入区域 */}
                <Collapsible open={isMultipleExpanded} onOpenChange={setIsMultipleExpanded}>
                    <CollapsibleTrigger asChild>
                        <Button variant="outline" className="w-full justify-between">
                            <span>批量下载多个视频</span>
                            {isMultipleExpanded ? (
                                <ChevronUp className="h-4 w-4"/>
                            ) : (
                                <ChevronDown className="h-4 w-4"/>
                            )}
                        </Button>
                    </CollapsibleTrigger>

                    <CollapsibleContent className="space-y-4 mt-4">
                        {/* 文件上传区域 */}
                        <div
                            className={cn(
                                'border-2 border-dashed rounded-lg p-4 text-center transition-colors',
                                dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300',
                                'hover:border-gray-400'
                            )}
                            onDragEnter={handleDrag}
                            onDragLeave={handleDrag}
                            onDragOver={handleDrag}
                            onDrop={handleDrop}
                        >
                            <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400"/>
                            <p className="text-sm text-gray-600 mb-2">
                                拖拽TXT文件到此处，或
                            </p>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => fileInputRef.current?.click()}
                                disabled={isLoading}
                            >
                                <Upload className="h-4 w-4 mr-2"/>
                                选择文件
                            </Button>
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept=".txt,text/plain"
                                onChange={handleFileSelect}
                                className="hidden"
                            />
                        </div>

                        {/* 多行文本输入 */}
                        <div className="space-y-2">
                            <div className="relative">
                                <Textarea
                                    placeholder={`每行一个视频链接（${YOUTUBE_CONSTANTS.MIN_MULTI_VIDEOS}-${YOUTUBE_CONSTANTS.MAX_MULTI_VIDEOS}个）...`}
                                    value={multipleUrls}
                                    onChange={(e) => handleMultipleUrlsChange(e.target.value)}
                                    disabled={isLoading}
                                    rows={6}
                                    className="resize-none"
                                />
                                {multipleUrls && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-2 top-2 h-8 w-8 p-0"
                                        onClick={clearMultipleUrls}
                                        disabled={isLoading}
                                    >
                                        <X className="h-4 w-4"/>
                                    </Button>
                                )}
                            </div>

                            {/* 多链接验证状态 */}
                            {multipleUrlsValidation && (
                                <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <Badge
                                            variant={multipleUrlsValidation.validCount > 0 ? 'default' : 'destructive'}>
                                            有效链接: {multipleUrlsValidation.validCount}/{multipleUrlsValidation.totalCount}
                                        </Badge>
                                        {multipleUrlsValidation.validCount < YOUTUBE_CONSTANTS.MIN_MULTI_VIDEOS && (
                                            <span className="text-sm text-red-600">
                        至少需要{YOUTUBE_CONSTANTS.MIN_MULTI_VIDEOS}个有效链接
                      </span>
                                        )}
                                        {multipleUrlsValidation.validCount > YOUTUBE_CONSTANTS.MAX_MULTI_VIDEOS && (
                                            <span className="text-sm text-red-600">
                        最多支持{YOUTUBE_CONSTANTS.MAX_MULTI_VIDEOS}个链接
                      </span>
                                        )}
                                    </div>

                                    {multipleUrlsValidation.invalidUrls.length > 0 && (
                                        <details className="text-sm">
                                            <summary className="cursor-pointer text-red-600">
                                                {multipleUrlsValidation.invalidUrls.length}个无效链接
                                            </summary>
                                            <div className="mt-2 space-y-1 text-red-500">
                                                {multipleUrlsValidation.invalidUrls.slice(0, 5).map((url, index) => (
                                                    <div key={index} className="truncate">• {url}</div>
                                                ))}
                                                {multipleUrlsValidation.invalidUrls.length > 5 && (
                                                    <div>...还有{multipleUrlsValidation.invalidUrls.length - 5}个</div>
                                                )}
                                            </div>
                                        </details>
                                    )}
                                </div>
                            )}
                        </div>
                    </CollapsibleContent>
                </Collapsible>

                {/* 提交按钮 */}
                <Button
                    onClick={handleSubmit}
                    disabled={!canSubmit()}
                    className="w-full"
                    size="lg"
                >
                    {isLoading ? (
                        <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin"/>
                            处理中...
                        </>
                    ) : (
                        <>
                            <Search className="h-4 w-4 mr-2"/>
                            开始解析
                        </>
                    )}
                </Button>
            </CardContent>
        </Card>
    );
}
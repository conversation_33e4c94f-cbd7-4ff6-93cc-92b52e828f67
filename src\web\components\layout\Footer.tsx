import Link from "next/link";

export function Footer() {
    return (
        <footer className="border-t bg-muted/50">
            <div className="container mx-auto px-4 py-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div>
                        <h3 className="font-semibold mb-4">YouTube Downloader</h3>
                        <p className="text-sm text-muted-foreground">
                            安全、快速、免费的YouTube视频下载服务
                        </p>
                    </div>

                    <div>
                        <h4 className="font-medium mb-4">法律信息</h4>
                        <ul className="space-y-2 text-sm">
                            <li>
                                <Link
                                    href="/privacy"
                                    className="text-muted-foreground hover:text-foreground transition-colors"
                                >
                                    隐私政策
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/terms"
                                    className="text-muted-foreground hover:text-foreground transition-colors"
                                >
                                    服务条款
                                </Link>
                            </li>
                        </ul>
                    </div>

                    <div>
                        <h4 className="font-medium mb-4">联系我们</h4>
                        <ul className="space-y-2 text-sm text-muted-foreground">
                            <li>邮箱：<EMAIL></li>
                            <li>隐私问题：<EMAIL></li>
                        </ul>
                    </div>
                </div>

                <div className="mt-8 pt-8 border-t text-center text-sm text-muted-foreground">
                    <p>© {new Date().getFullYear()} YouTube Downloader. 保留所有权利。</p>
                    <p className="mt-2">
                        本服务仅供个人非商业用途。用户需自行承担版权责任。
                    </p>
                </div>
            </div>
        </footer>
    );
}

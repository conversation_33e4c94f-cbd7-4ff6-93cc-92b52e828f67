using FluentValidation;
using Shared.Common;

namespace Shared.DTOs;

public record CreateVideoTaskRequest(string VideoId, string? OutputFormat, string? Quality, int? StartTime, int? EndTime);

public class CreateVideoTaskRequestValidator : AbstractValidator<CreateVideoTaskRequest>
{
    public CreateVideoTaskRequestValidator()
    {
        RuleFor(x => x.VideoId).NotEmpty().WithMessage("视频ID不能为空").Matches(@"^[a-zA-Z0-9_-]{11}$").WithMessage("视频ID格式不正确");

        RuleFor(x => x.OutputFormat).Must(format => format == null || IsValidVideoFormat(format)).WithMessage("输出格式不支持，支持的格式：mp4, webm, mkv");

        RuleFor(x => x.Quality).Must(quality => quality == null || IsValidVideoQuality(quality)).WithMessage("视频质量不支持");

        RuleFor(x => x.StartTime).GreaterThanOrEqualTo(0).When(x => x.StartTime.HasValue).WithMessage("开始时间不能小于0");

        RuleFor(x => x.EndTime).GreaterThan(x => x.StartTime).When(x => x.EndTime.HasValue && x.StartTime.HasValue).WithMessage("结束时间必须大于开始时间");

        RuleFor(x => x.EndTime).LessThanOrEqualTo(7200).When(x => x.EndTime.HasValue).WithMessage("结束时间不能超过7200秒");
    }

    private static bool IsValidVideoFormat(string format)
    {
        var validFormats = new[] { "mp4", "webm", "mkv" };
        return validFormats.Contains(format.ToLower());
    }

    private static bool IsValidVideoQuality(string quality)
    {
        var validQualities = new[] { "144p", "240p", "360p", "480p", "720p", "1080p", "1440p", "2160p", "best", "worst" };
        return validQualities.Contains(quality.ToLower());
    }
}

public record CreateAudioTaskRequest(string VideoId, string OutputFormat, string? Quality, int? StartTime, int? EndTime);

public class CreateAudioTaskRequestValidator : AbstractValidator<CreateAudioTaskRequest>
{
    public CreateAudioTaskRequestValidator()
    {
        RuleFor(x => x.VideoId).NotEmpty().WithMessage("视频ID不能为空").Matches(@"^[a-zA-Z0-9_-]{11}$").WithMessage("视频ID格式不正确");

        RuleFor(x => x.OutputFormat).NotEmpty().WithMessage("输出格式不能为空").Must(IsValidAudioFormat).WithMessage("输出格式不支持，支持的格式：mp3, aac, wav, flac, ogg");

        RuleFor(x => x.Quality).Must(quality => quality == null || IsValidAudioQuality(quality)).WithMessage("音频质量不支持");

        RuleFor(x => x.StartTime).GreaterThanOrEqualTo(0).When(x => x.StartTime.HasValue).WithMessage("开始时间不能小于0");

        RuleFor(x => x.EndTime).GreaterThan(x => x.StartTime).When(x => x.EndTime.HasValue && x.StartTime.HasValue).WithMessage("结束时间必须大于开始时间");
    }

    private static bool IsValidAudioFormat(string format)
    {
        var validFormats = new[] { "mp3", "aac", "wav", "flac", "ogg" };
        return validFormats.Contains(format.ToLower());
    }

    private static bool IsValidAudioQuality(string quality)
    {
        var validQualities = new[] { "64k", "128k", "192k", "256k", "320k", "best" };
        return validQualities.Contains(quality.ToLower());
    }
}

public record CreateGifTaskRequest(string VideoId, int StartTime, int EndTime, int? Fps, int? Width);

public class CreateGifTaskRequestValidator : AbstractValidator<CreateGifTaskRequest>
{
    public CreateGifTaskRequestValidator()
    {
        RuleFor(x => x.VideoId).NotEmpty().WithMessage("视频ID不能为空").Matches(@"^[a-zA-Z0-9_-]{11}$").WithMessage("视频ID格式不正确");

        RuleFor(x => x.StartTime).GreaterThanOrEqualTo(0).WithMessage("开始时间不能小于0");

        RuleFor(x => x.EndTime).GreaterThan(x => x.StartTime).WithMessage("结束时间必须大于开始时间");

        RuleFor(x => x.EndTime - x.StartTime).LessThanOrEqualTo(30).WithMessage("GIF时长不能超过30秒");

        RuleFor(x => x.Fps).InclusiveBetween(5, 30).When(x => x.Fps.HasValue).WithMessage("帧率必须在5-30之间");

        RuleFor(x => x.Width).InclusiveBetween(100, 1920).When(x => x.Width.HasValue).WithMessage("宽度必须在100-1920之间");
    }
}

public record CreateBatchTaskRequest(
    string Name,
    BatchTaskType SourceType,
    string SourceId,
    string SourceUrl,
    List<string> SelectedVideoIds,
    string Configuration);

public class CreateBatchTaskRequestValidator : AbstractValidator<CreateBatchTaskRequest>
{
    public CreateBatchTaskRequestValidator()
    {
        RuleFor(x => x.Name).NotEmpty().WithMessage("任务名称不能为空").MaximumLength(100).WithMessage("任务名称不能超过100个字符");

        RuleFor(x => x.SourceType).IsInEnum().WithMessage("源类型无效");

        RuleFor(x => x.SourceId).NotEmpty().WithMessage("源ID不能为空").MaximumLength(50).WithMessage("源ID不能超过50个字符");

        RuleFor(x => x.SourceUrl).NotEmpty().WithMessage("源URL不能为空").Must(BeValidUrl).WithMessage("源URL格式不正确");

        RuleFor(x => x.SelectedVideoIds).NotEmpty().WithMessage("必须选择至少一个视频").Must(x => x.Count <= 100).WithMessage("最多只能选择100个视频");

        RuleForEach(x => x.SelectedVideoIds).NotEmpty().WithMessage("视频ID不能为空").Matches(@"^[a-zA-Z0-9_-]{11}$").WithMessage("视频ID格式不正确");

        RuleFor(x => x.Configuration).NotEmpty().WithMessage("配置信息不能为空").MaximumLength(2000).WithMessage("配置信息不能超过2000个字符");
    }

    private static bool BeValidUrl(string url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out var result) && (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
}

public record TaskProgressUpdateRequest(Guid TaskId, Guid UserId, int Progress, string StatusMessage, WorkerTaskStatus? NewStatus = null);

public class TaskProgressUpdateRequestValidator : AbstractValidator<TaskProgressUpdateRequest>
{
    public TaskProgressUpdateRequestValidator()
    {
        RuleFor(x => x.TaskId).NotEmpty().WithMessage("任务ID不能为空");

        RuleFor(x => x.UserId).NotEmpty().WithMessage("用户ID不能为空");

        RuleFor(x => x.Progress).InclusiveBetween(0, 100).WithMessage("进度必须在0-100之间");

        RuleFor(x => x.StatusMessage).NotEmpty().WithMessage("状态消息不能为空").MaximumLength(200).WithMessage("状态消息不能超过200个字符");

        RuleFor(x => x.NewStatus).IsInEnum().When(x => x.NewStatus.HasValue).WithMessage("任务状态无效");
    }
}

public record TaskCompletionRequest(
    Guid TaskId,
    bool IsSuccess,
    string? ResultPath = null,
    long? FileSize = null,
    string? ErrorMessage = null,
    string? ErrorCode = null);

public class TaskCompletionRequestValidator : AbstractValidator<TaskCompletionRequest>
{
    public TaskCompletionRequestValidator()
    {
        RuleFor(x => x.TaskId).NotEmpty().WithMessage("任务ID不能为空");

        RuleFor(x => x.ResultPath).NotEmpty().When(x => x.IsSuccess).WithMessage("任务成功时必须提供结果路径");

        RuleFor(x => x.FileSize).GreaterThan(0).When(x => x.IsSuccess && x.FileSize.HasValue).WithMessage("文件大小必须大于0");

        RuleFor(x => x.ErrorMessage).NotEmpty().When(x => !x.IsSuccess).WithMessage("任务失败时必须提供错误消息");

        RuleFor(x => x.ErrorCode).NotEmpty().When(x => !x.IsSuccess).WithMessage("任务失败时必须提供错误代码");
    }
}

public record WorkerTaskResponse(
    Guid Id,
    Guid UserId,
    Guid? BatchTaskId,
    string Name,
    WorkerTaskType TaskType,
    string VideoId,
    string? VideoTitle,
    string? VideoUrl,
    WorkerTaskStatus Status,
    int Progress,
    WorkerTaskPriority Priority,
    string? OutputFormat,
    string? Quality,
    int? StartTime,
    int? EndTime,
    string? ResultPath,
    long? FileSize,
    string? ErrorMessage,
    DateTime CreatedAt,
    DateTime? StartedAt,
    DateTime? CompletedAt,
    DateTime? FileExpiresAt);

public record BatchTaskResponse(
    Guid Id,
    Guid UserId,
    string Name,
    BatchTaskType SourceType,
    string SourceId,
    string SourceUrl,
    string? SourceTitle,
    int TotalVideoCount,
    int SelectedVideoCount,
    BatchTaskStatus Status,
    int Progress,
    int CompletedTaskCount,
    int FailedTaskCount,
    DateTime CreatedAt,
    DateTime? StartedAt,
    DateTime? CompletedAt,
    List<WorkerTaskResponse> WorkerTasks);

public record TaskQueryFilter(
    WorkerTaskStatus? Status = null,
    WorkerTaskType? TaskType = null,
    DateTime? StartDate = null,
    DateTime? EndDate = null,
    int Skip = 0,
    int Take = 20);

public record TaskStatsResponse(int TotalTasks, int PendingTasks, int ProcessingTasks, int CompletedTasks, int FailedTasks, int CancelledTasks);

public record CreateThumbnailTaskRequest(string VideoId, string? Quality = "maxresdefault");

public class CreateThumbnailTaskRequestValidator : AbstractValidator<CreateThumbnailTaskRequest>
{
    public CreateThumbnailTaskRequestValidator()
    {
        RuleFor(x => x.VideoId).NotEmpty().WithMessage("视频ID不能为空").Matches(@"^[a-zA-Z0-9_-]{11}$").WithMessage("视频ID格式不正确");

        RuleFor(x => x.Quality).Must(quality => quality == null || IsValidThumbnailQuality(quality))
            .WithMessage("缩略图质量不支持，支持的质量：default, mqdefault, hqdefault, sddefault, maxresdefault");
    }

    private static bool IsValidThumbnailQuality(string quality)
    {
        var validQualities = new[] { "default", "mqdefault", "hqdefault", "sddefault", "maxresdefault" };
        return validQualities.Contains(quality.ToLower());
    }
}

public record UpdateTaskStatusRequest(WorkerTaskStatus Status, string? ErrorMessage = null, int? Progress = null);

public class UpdateTaskStatusRequestValidator : AbstractValidator<UpdateTaskStatusRequest>
{
    public UpdateTaskStatusRequestValidator()
    {
        RuleFor(x => x.Status).IsInEnum().WithMessage("任务状态无效");

        RuleFor(x => x.Progress).InclusiveBetween(0, 100).When(x => x.Progress.HasValue).WithMessage("进度必须在0-100之间");
    }
}

public record BatchTaskListResponse(List<BatchTaskResponse> BatchTasks, int TotalCount, int Page, int PageSize);
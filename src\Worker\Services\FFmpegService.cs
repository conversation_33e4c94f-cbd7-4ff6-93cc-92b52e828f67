using CliWrap;
using Shared.Common;

namespace Worker.Services;

public class FFmpegService(ILogger<FFmpegService> logger, IConfiguration configuration)
{
    private readonly string _ffmpegPath = configuration["Worker:FfmpegPath"] ?? throw new InvalidOperationException("FFmpeg路径未配�?);

    public async Task<ServiceResult<DownloadResult>> ClipVideoAsync(string inputPath, string outputPath, int? startTime, int? endTime,
        CancellationToken cancellationToken = default)
    {
        return await ClipMediaAsync(inputPath, outputPath, startTime, endTime, "视频剪辑", cancellationToken);
    }

    public async Task<ServiceResult<DownloadResult>> ClipAudioAsync(string inputPath, string outputPath, int? startTime, int? endTime,
        CancellationToken cancellationToken = default)
    {
        return await ClipMediaAsync(inputPath, outputPath, startTime, endTime, "音频剪辑", cancellationToken);
    }

    private async Task<ServiceResult<DownloadResult>> ClipMediaAsync(string inputPath, string outputPath, int? startTime, int? endTime,
        string operationName = "媒体剪辑", CancellationToken cancellationToken = default)
    {
        try
        {
            var args = new List<string> { "-i", inputPath };
            if (startTime.HasValue) args.AddRange(["-ss", FormatTime(startTime.Value)]);
            if (endTime.HasValue) args.AddRange(["-t", FormatTime(endTime.Value - (startTime ?? 0))]);
            args.AddRange(["-c", "copy", "-y", outputPath]);

            var result = await ExecuteFFmpegCommandAsync(args, cancellationToken);
            if (result.ExitCode != 0) return ServiceResult<DownloadResult>.Failure($"{operationName}失败，退出代�? {result.ExitCode}");
            return CreateDownloadResult(outputPath);
        }
        catch (Exception ex)
        {
            return HandleError(ex, operationName, inputPath, logger);
        }
    }

    public async Task<ServiceResult<DownloadResult>> CreateGifAsync(string inputVideoPath, string outputPath, int startTime, int endTime, int fps = 10,
        int width = 400, CancellationToken cancellationToken = default)
    {
        try
        {
            var duration = Math.Min(endTime - startTime, 30);
            fps = Math.Clamp(fps, 5, 20);
            width = Math.Clamp(width, 100, 800);
            var paletteFile = Path.ChangeExtension(outputPath, ".palette.png");

            // 生成调色�?            var paletteArgs = new List<string>
            {
                "-i", inputVideoPath, "-ss", FormatTime(startTime), "-t", FormatTime(duration), "-vf",
                $"fps={fps},scale={width}:-1:flags=lanczos,palettegen=stats_mode=diff", "-y", paletteFile
            }
            ;
            var paletteResult = await ExecuteFFmpegCommandAsync(paletteArgs, cancellationToken);
            if (paletteResult.ExitCode != 0) return ServiceResult<DownloadResult>.Failure($"生成GIF调色板失败，退出代�? {paletteResult.ExitCode}");

            // 生成GIF
            var gifArgs = new List<string>
            {
                "-i", inputVideoPath, "-i", paletteFile, "-ss", FormatTime(startTime), "-t", FormatTime(duration), "-lavfi",
                $"fps={fps},scale={width}:-1:flags=lanczos[x];[x][1:v]paletteuse=dither=bayer:bayer_scale=5:diff_mode=rectangle", "-y", outputPath
            };
            var gifResult = await ExecuteFFmpegCommandAsync(gifArgs, cancellationToken);

            try
            {
                if (File.Exists(paletteFile)) File.Delete(paletteFile);
            }
            catch
            {
            }

            if (gifResult.ExitCode != 0) return ServiceResult<DownloadResult>.Failure($"生成GIF失败，退出代�? {gifResult.ExitCode}");
            return CreateDownloadResult(outputPath, "gif", TimeSpan.FromSeconds(duration));
        }
        catch (Exception ex)
        {
            return HandleError(ex, "创建GIF", inputVideoPath, logger);
        }
    }

    public async Task<ServiceResult<DownloadResult>> CreateRingtoneAsync(string inputAudioPath, string outputPath, int startTime, int endTime,
        string format = "m4r", CancellationToken cancellationToken = default)
    {
        try
        {
            var duration = Math.Min(endTime - startTime, 30);
            var fadeTime = Math.Min(2, duration / 4);

            var args = new List<string>
            {
                "-i", inputAudioPath, "-ss", FormatTime(startTime), "-t", FormatTime(duration),
                "-acodec", format == "mp3" ? "mp3" : "aac", "-ab", "128k", "-ar", "44100", "-ac", "2",
                "-af", $"afade=t=in:ss=0:d={fadeTime},afade=t=out:st={duration - fadeTime}:d={fadeTime}",
                "-y", outputPath
            };

            var result = await ExecuteFFmpegCommandAsync(args, cancellationToken);
            if (result.ExitCode != 0) return ServiceResult<DownloadResult>.Failure($"铃声制作失败，退出代�? {result.ExitCode}");
            return CreateDownloadResult(outputPath, format, TimeSpan.FromSeconds(duration));
        }
        catch (Exception ex)
        {
            return HandleError(ex, "制作铃声", inputAudioPath, logger);
        }
    }

    private async Task<CommandResult> ExecuteFFmpegCommandAsync(List<string> args, CancellationToken cancellationToken)
    {
        return await Cli.Wrap(_ffmpegPath).WithArguments(args).WithValidation(CommandResultValidation.None).ExecuteAsync(cancellationToken);
    }

    private static ServiceResult<DownloadResult> CreateDownloadResult(string outputPath, string? format = null, TimeSpan? duration = null)
    {
        var fileInfo = new FileInfo(outputPath);
        var actualFormat = format ?? Path.GetExtension(outputPath).TrimStart('.');
        var downloadResult = new DownloadResult(outputPath, fileInfo.Length, actualFormat, duration ?? TimeSpan.Zero);
        return ServiceResult<DownloadResult>.Success(downloadResult);
    }

    private static string FormatTime(int seconds)
    {
        return TimeSpan.FromSeconds(seconds).ToString(@"hh\:mm\:ss");
    }

    private static ServiceResult<DownloadResult> HandleError(Exception ex, string operation, string inputPath, ILogger logger)
    {
        logger.LogError(ex, "{Operation}时发生错�? {InputPath}", operation, inputPath);
        return ServiceResult<DownloadResult>.Failure($"{operation}失败: {ex.Message}");
    }

    public async Task<bool> IsFFmpegAvailableAsync()
    {
        try
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
            var result = await Cli.Wrap(_ffmpegPath).WithArguments("-version").WithValidation(CommandResultValidation.None).ExecuteAsync(cts.Token);
            return result.ExitCode == 0;
        }
        catch
        {
            return false;
        }
    }
}
import {AlertTriangle, Home, RefreshCw} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';

interface ApiErrorFallbackProps {
    error?: string;
    title?: string;
    description?: string;
    onRetry?: () => void;
    onGoHome?: () => void;
    showRetry?: boolean;
    showGoHome?: boolean;
}

export function ApiErrorFallback({
                                     error = '请求失败',
                                     title = '出现了一些问题',
                                     description,
                                     onRetry,
                                     onGoHome,
                                     showRetry = true,
                                     showGoHome = true,
                                 }: ApiErrorFallbackProps) {
    const defaultDescription = description || '无法获取数据，请检查网络连接或稍后重试。';

    return (
        <div className="flex items-center justify-center min-h-[400px] p-4">
            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                        <AlertTriangle className="h-6 w-6 text-red-600"/>
                    </div>
                    <CardTitle className="text-lg">{title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                    <div className="space-y-2">
                        <p className="text-sm text-muted-foreground">
                            {defaultDescription}
                        </p>
                        {error && (
                            <details className="text-xs text-red-600">
                                <summary className="cursor-pointer">错误详情</summary>
                                <p className="mt-2 text-left bg-red-50 p-2 rounded border">
                                    {error}
                                </p>
                            </details>
                        )}
                    </div>

                    <div className="flex flex-col sm:flex-row gap-2 justify-center">
                        {showRetry && onRetry && (
                            <Button onClick={onRetry} size="sm">
                                <RefreshCw className="h-4 w-4 mr-2"/>
                                重试
                            </Button>
                        )}
                        {showGoHome && onGoHome && (
                            <Button variant="outline" onClick={onGoHome} size="sm">
                                <Home className="h-4 w-4 mr-2"/>
                                返回首页
                            </Button>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

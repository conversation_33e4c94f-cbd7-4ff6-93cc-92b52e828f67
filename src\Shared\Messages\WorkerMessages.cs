using Shared.Common;

namespace Shared.Messages;

public record WorkerHeartbeatMessage(
    Guid NodeId,
    string NodeName,
    WorkerStatus Status,
    double CpuUsage,
    double MemoryUsage,
    int ActiveTasks,
    int TotalProcessed,
    DateTime LastHeartbeat);

public record WorkerNotificationMessage(
    string Type,
    string Title,
    string Message,
    WorkerAlertLevel Severity,
    Dictionary<string, object>? Data,
    DateTime CreatedAt);
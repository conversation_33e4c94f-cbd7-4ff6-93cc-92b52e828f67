using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Api.Data.Models;

public class WorkerMetrics
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public required Guid WorkerId { get; set; }
    public double CpuUsagePercent { get; set; }
    public double MemoryUsagePercent { get; set; }
    public double DiskUsagePercent { get; set; }
    public int ActiveTasks { get; set; }
    public int TotalProcessedTasks { get; set; }
    public double NetworkReceivedGB { get; set; }
    public double NetworkSentGB { get; set; }
    public double NetworkBandwidthMbps { get; set; }
    public int ActiveConnections { get; set; }
    public DateTime RecordedAt { get; init; }

    public Worker Worker { get; set; } = null!;
}

public class WorkerMetricsConfiguration : IEntityTypeConfiguration<WorkerMetrics>
{
    public void Configure(EntityTypeBuilder<WorkerMetrics> builder)
    {
        builder.ToTable("worker_metrics", "public");
        builder.HasKey(m => m.Id);
        builder.Property(m => m.Id).ValueGeneratedNever();
        builder.Property(m => m.WorkerId).IsRequired().HasColumnName("worker_id");
        builder.Property(m => m.CpuUsagePercent).IsRequired().HasColumnName("cpu_usage_percent");
        builder.Property(m => m.MemoryUsagePercent).IsRequired().HasColumnName("memory_usage_percent");
        builder.Property(m => m.DiskUsagePercent).IsRequired().HasColumnName("disk_usage_percent");
        builder.Property(m => m.ActiveTasks).IsRequired().HasColumnName("active_tasks");
        builder.Property(m => m.TotalProcessedTasks).IsRequired().HasColumnName("total_processed_tasks");
        builder.Property(m => m.NetworkReceivedGB).IsRequired().HasColumnName("network_received_gb");
        builder.Property(m => m.NetworkSentGB).IsRequired().HasColumnName("network_sent_gb");
        builder.Property(m => m.NetworkBandwidthMbps).IsRequired().HasColumnName("network_bandwidth_mbps");
        builder.Property(m => m.ActiveConnections).IsRequired().HasColumnName("active_connections");
        builder.Property(m => m.RecordedAt).IsRequired().HasColumnName("recorded_at").ValueGeneratedOnAdd();

        builder.HasIndex(m => m.WorkerId).HasDatabaseName("ix_worker_metrics_worker_id");
        builder.HasIndex(m => m.RecordedAt).HasDatabaseName("ix_worker_metrics_recorded_at");
        builder.HasIndex(m => new { m.WorkerId, m.RecordedAt }).HasDatabaseName("ix_worker_metrics_worker_time_desc").IsDescending(false, true);

        builder.HasOne(m => m.Worker).WithMany(w => w.Metrics).HasForeignKey(m => m.WorkerId).OnDelete(DeleteBehavior.Cascade);
    }
}
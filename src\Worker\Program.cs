using Scalar.AspNetCore;
using Worker.Endpoints;
using Worker.Services;

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddHttpClient();

builder.Services.AddSingleton<MetricsService>();
// builder.Services.AddSingleton<HeartbeatService>();
// builder.Services.AddHostedService(provider => provider.GetRequiredService<HeartbeatService>());
//
// builder.Services.AddScoped<HealthCheckService>();
//
builder.Services.AddScoped<FFmpegService>();
builder.Services.AddScoped<YtDlpService>();
builder.Services.AddScoped<RapidApiService>();
builder.Services.AddScoped<YouTubeService>();
builder.Services.AddScoped<TaskProcessorService>();

// builder.Services.AddMassTransit(x =>
// {
//     x.AddConsumer<TaskConsumer>();
//     x.UsingRabbitMq((context, cfg) =>
//     {
//         cfg.Host(builder.Configuration.GetConnectionString("RabbitMQ"));
//         var queueName = builder.Configuration.GetValue<string>("RabbitMQ:TaskQueueName");
//         cfg.ReceiveEndpoint(queueName, e =>
//         {
//             e.ConfigureConsumer<TaskConsumer>(context);
//             e.Durable = true;
//         });
//         cfg.ConfigureEndpoints(context);
//     });
// });

// builder.Services.AddHttpClient<NodeLifecycleService>();
// builder.Services.AddSingleton<NodeLifecycleService>();
// builder.Services.AddHostedService(provider => provider.GetRequiredService<NodeLifecycleService>());
// builder.Services.AddHostedService<FileCleanupService>();

builder.Services.AddOpenApi();

var app = builder.Build();
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference();
}

app.MapWorkerEndpoints();
app.MapGet("/", () => Results.Ok(new
{
    service = "YouTube Downloader Worker Node",
    status = "Running",
    timestamp = DateTime.UtcNow
}));

app.Run();
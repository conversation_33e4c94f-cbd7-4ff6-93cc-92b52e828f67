using System.Diagnostics;
using System.Management;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using Shared.Common;

namespace Worker.Services;

public class MetricsService : IDisposable
{
    private readonly PerformanceCounter? _cpuCounter;
    private readonly bool _isLinux = RuntimeInformation.IsOSPlatform(OSPlatform.Linux);
    private readonly bool _isWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
    private readonly ILogger<MetricsService> _logger;
    private readonly PerformanceCounter? _memoryCounter;
    private int _completedTasks;
    private int _failedTasks;
    private int _processingTasks;

    public MetricsService(ILogger<MetricsService> logger)
    {
        _logger = logger;
        _logger.LogDebug("初始化MetricsService，平台: {Platform}", _isWindows ? "Windows" : _isLinux ? "Linux" : "Unknown");

        if (_isWindows)
            try
            {
#pragma warning disable CA1416
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
                _cpuCounter.NextValue();
#pragma warning restore CA1416
                _logger.LogDebug("Windows性能计数器初始化成功");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Windows性能计数器初始化失败");
                _cpuCounter?.Dispose();
                _memoryCounter?.Dispose();
                _cpuCounter = null;
                _memoryCounter = null;
            }
    }

    public void Dispose()
    {
        _cpuCounter?.Dispose();
        _memoryCounter?.Dispose();
        GC.SuppressFinalize(this);
    }

    public async Task<SystemMetrics> GetSystemMetricsAsync()
    {
        try
        {
            _logger.LogDebug("开始获取系统指标");

            var cpuCores = Environment.ProcessorCount;
            var cpuUsage = await GetCpuUsageAsync();
            var (totalMemoryGB, memoryUsage) = await GetMemoryMetricsAsync();
            var (totalDiskGB, diskUsage) = GetDiskMetrics();
            var (receivedGB, sentGB, bandwidthMbps) = GetNetworkMetrics();
            var connections = await GetActiveConnectionsAsync();

            _logger.LogDebug("系统指标获取完成 - CPU: {CpuUsage:F1}%, 内存: {MemoryUsage:F1}%, 磁盘: {DiskUsage:F1}%, 连接数: {Connections}", cpuUsage, memoryUsage, diskUsage,
                connections);

            return new SystemMetrics(cpuCores, cpuUsage, totalMemoryGB, memoryUsage, totalDiskGB, diskUsage, receivedGB, sentGB, bandwidthMbps, connections,
                _processingTasks, _completedTasks, _failedTasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统指标时发生错误");
            throw;
        }
    }

    private async Task<double> GetCpuUsageAsync()
    {
#pragma warning disable CA1416
        if (_isWindows && _cpuCounter != null) return _cpuCounter.NextValue();
#pragma warning restore CA1416

        if (_isLinux)
            try
            {
                var stat1 = await ReadProcStatAsync();
                await Task.Delay(100);
                var stat2 = await ReadProcStatAsync();

                var idle1 = stat1.idle + stat1.iowait;
                var idle2 = stat2.idle + stat2.iowait;
                var total1 = stat1.user + stat1.nice + stat1.system + stat1.idle + stat1.iowait + stat1.irq + stat1.softirq;
                var total2 = stat2.user + stat2.nice + stat2.system + stat2.idle + stat2.iowait + stat2.irq + stat2.softirq;

                var totalDiff = total2 - total1;
                var idleDiff = idle2 - idle1;

                return totalDiff > 0 ? (1.0 - (double)idleDiff / totalDiff) * 100 : 0.0;
            }
            catch
            {
                return 0.0;
            }

        return 0.0;
    }

    private async Task<(double totalGB, double usagePercent)> GetMemoryMetricsAsync()
    {
        if (_isWindows && _memoryCounter != null)
            try
            {
#pragma warning disable CA1416
                using var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem");
                foreach (var obj in searcher.Get())
                {
                    var totalBytes = Convert.ToInt64(obj["TotalPhysicalMemory"]);
                    var totalMemoryGB = totalBytes / (1024.0 * 1024.0 * 1024.0);
                    var availableMB = _memoryCounter.NextValue();
#pragma warning restore CA1416
                    var totalMemoryMB = totalMemoryGB * 1024;
                    var usedMemoryMB = totalMemoryMB - availableMB;
                    var usagePercent = usedMemoryMB / totalMemoryMB * 100;
                    return (totalMemoryGB, usagePercent);
                }
            }
            catch
            {
                return (0.0, 0.0);
            }

        if (_isLinux)
            try
            {
                var lines = await File.ReadAllLinesAsync("/proc/meminfo");
                var memTotal = ParseMemInfoValue(lines, "MemTotal:");
                var memAvailable = ParseMemInfoValue(lines, "MemAvailable:");

                var totalGB = memTotal / (1024.0 * 1024.0);
                var usagePercent = memTotal > 0 ? (double)(memTotal - memAvailable) / memTotal * 100 : 0.0;
                return (totalGB, usagePercent);
            }
            catch
            {
                return (0.0, 0.0);
            }

        return (0.0, 0.0);
    }

    private static (double totalGB, double usagePercent) GetDiskMetrics()
    {
        try
        {
            var drives = DriveInfo.GetDrives().Where(d => d is { IsReady: true, DriveType: DriveType.Fixed });
            long totalSize = 0;
            long usedSize = 0;

            foreach (var drive in drives)
            {
                totalSize += drive.TotalSize;
                usedSize += drive.TotalSize - drive.AvailableFreeSpace;
            }

            var totalGB = totalSize / (1024.0 * 1024.0 * 1024.0);
            var usagePercent = totalSize > 0 ? (double)usedSize / totalSize * 100 : 0.0;
            return (totalGB, usagePercent);
        }
        catch
        {
            return (0.0, 0.0);
        }
    }

    private static (double ReceivedGB, double SentGB, double BandwidthMbps) GetNetworkMetrics()
    {
        try
        {
            var interfaces = NetworkInterface.GetAllNetworkInterfaces().Where(ni =>
                ni.OperationalStatus == OperationalStatus.Up && ni.NetworkInterfaceType != NetworkInterfaceType.Loopback);

            long totalReceived = 0;
            long totalSent = 0;
            double maxSpeed = 0;

            foreach (var ni in interfaces)
            {
                var stats = ni.GetIPv4Statistics();
                totalReceived += stats.BytesReceived;
                totalSent += stats.BytesSent;

                if (ni.Speed > 0 && ni.Speed != long.MaxValue) maxSpeed = Math.Max(maxSpeed, ni.Speed / 1_000_000.0);
            }

            var receivedGB = totalReceived / (1024.0 * 1024.0 * 1024.0);
            var sentGB = totalSent / (1024.0 * 1024.0 * 1024.0);

            return (receivedGB, sentGB, maxSpeed);
        }
        catch
        {
            return (0.0, 0.0, 0.0);
        }
    }

    private async Task<int> GetActiveConnectionsAsync()
    {
        if (_isWindows)
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "netstat",
                    Arguments = "-an",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process == null) return 0;

                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                return output.Split('\n').Count(line => line.Contains("ESTABLISHED"));
            }
            catch
            {
                return 0;
            }

        if (_isLinux)
            try
            {
                var tcpConnections = 0;

                if (File.Exists("/proc/net/tcp"))
                {
                    var lines = await File.ReadAllLinesAsync("/proc/net/tcp");
                    tcpConnections += lines.Skip(1).Count(line => !string.IsNullOrWhiteSpace(line));
                }

                if (File.Exists("/proc/net/tcp6"))
                {
                    var lines = await File.ReadAllLinesAsync("/proc/net/tcp6");
                    tcpConnections += lines.Skip(1).Count(line => !string.IsNullOrWhiteSpace(line));
                }

                return tcpConnections;
            }
            catch
            {
                return 0;
            }

        return 0;
    }

    private static async Task<(long user, long nice, long system, long idle, long iowait, long irq, long softirq)> ReadProcStatAsync()
    {
        var lines = await File.ReadAllLinesAsync("/proc/stat");
        var cpuLine = lines[0];
        var parts = cpuLine.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        return (long.Parse(parts[1]), long.Parse(parts[2]), long.Parse(parts[3]), long.Parse(parts[4]), long.Parse(parts[5]), long.Parse(parts[6]),
            long.Parse(parts[7]));
    }

    private static long ParseMemInfoValue(string[] lines, string key)
    {
        var line = lines.FirstOrDefault(l => l.StartsWith(key));
        if (line == null) return 0;

        var parts = line.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        return parts.Length > 1 && long.TryParse(parts[1], out var value) ? value : 0;
    }

    public void RecordTask(WorkerTaskStatus status)
    {
        switch (status)
        {
            case WorkerTaskStatus.Processing:
                Interlocked.Increment(ref _processingTasks);
                _logger.LogDebug("任务开始，当前处理中任务数: {ProcessingTasks}", _processingTasks);
                break;
            case WorkerTaskStatus.Completed:
                Interlocked.Decrement(ref _processingTasks);
                Interlocked.Increment(ref _completedTasks);
                _logger.LogDebug("任务完成，处理中: {ProcessingTasks}, 已完成: {CompletedTasks}", _processingTasks, _completedTasks);
                break;
            case WorkerTaskStatus.Failed:
                Interlocked.Decrement(ref _processingTasks);
                Interlocked.Increment(ref _failedTasks);
                _logger.LogDebug("任务失败，处理中: {ProcessingTasks}, 已失败: {FailedTasks}", _processingTasks, _failedTasks);
                break;
            case WorkerTaskStatus.Cancelled:
                Interlocked.Decrement(ref _processingTasks);
                _logger.LogDebug("任务取消，当前处理中任务数: {ProcessingTasks}", _processingTasks);
                break;
        }
    }
}
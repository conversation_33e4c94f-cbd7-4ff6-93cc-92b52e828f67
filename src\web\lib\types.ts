export interface YouTubeVideoInfo {
    videoId: string;
    title: string;
    description?: string;
    duration?: string;
    durationSeconds?: number;
    viewCount?: number;
    likeCount?: number;
    commentCount?: number;
    publishedAt?: string;
    channelId?: string;
    channelTitle?: string;
    channelUrl?: string;
    thumbnails?: {
        default?: string;
        medium?: string;
        high?: string;
        maxres?: string;
    };
    formats?: VideoFormat[];
    audioFormats?: AudioFormat[];
    subtitles?: SubtitleTrack[];
    isLive?: boolean;
    isUpcoming?: boolean;
    ageRestricted?: boolean;
    availability?: string;
}

export interface VideoFormat {
    formatId: string;
    ext: string;
    resolution?: string;
    width?: number;
    height?: number;
    fps?: number;
    vcodec?: string;
    acodec?: string;
    filesize?: number;
    filesizeApprox?: number;
    quality?: string;
    hasAudio?: boolean;
    hasVideo?: boolean;
    url?: string;
}

export interface AudioFormat {
    formatId: string;
    ext: string;
    acodec?: string;
    abr?: number;
    filesize?: number;
    filesizeApprox?: number;
    quality?: string;
    url?: string;
}

export interface SubtitleTrack {
    language: string;
    languageCode: string;
    isAutoGenerated: boolean;
    url?: string;
    formats?: string[];
}

export interface YouTubePlaylistInfo {
    playlistId: string;
    title: string;
    description?: string;
    videoCount: number;
    channelTitle?: string;
    videos: YouTubeVideoInfo[];
}

export interface YouTubeChannelInfo {
    channelId: string;
    title: string;
    description?: string;
    subscriberCount?: number;
    videoCount: number;
    videos: YouTubeVideoInfo[];
}

export interface UrlSubmissionData {
    type: 'single' | 'multiple';
    urls: string[];
    videoIds?: string[];
}

export interface ApiError {
    message: string;
    code?: string;
    details?: any;
}

export interface LoadingState {
    isLoading: boolean;
    message?: string;
}

export interface ValidationError {
    field: string;
    message: string;
}

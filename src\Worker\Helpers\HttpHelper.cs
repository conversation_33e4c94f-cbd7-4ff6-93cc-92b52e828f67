using System.Text.Json;
using Shared.Common;

namespace Worker.Helpers;

public static class HttpHelper
{
    public static async Task<ServiceResult<T>> PostJsonAsync<T>(HttpClient httpClient, string url, object request, ILogger logger, string operation = "HTTP请求")
    {
        try
        {
            logger.LogDebug("调用 {Operation}: {Url}", operation, url);

            var response = await httpClient.PostAsJsonAsync(url, request);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogWarning("{Operation} 失败: {StatusCode} {Content}", operation, response.StatusCode, responseContent);
                return ServiceResult<T>.Failure($"{operation}失败", "HTTP_ERROR");
            }

            var result = JsonSerializer.Deserialize<T>(responseContent, JsonHelper.GetCamelCaseOptions());
            return result != null ? ServiceResult<T>.Success(result) : ServiceResult<T>.Failure("响应解析失败", "PARSE_ERROR");
        }
        catch (HttpRequestException ex)
        {
            logger.LogError(ex, "{Operation} HTTP错误: {Url}", operation, url);
            return ServiceResult<T>.Failure("网络请求失败", "NETWORK_ERROR");
        }
        catch (TaskCanceledException ex)
        {
            logger.LogError(ex, "{Operation} 超时: {Url}", operation, url);
            return ServiceResult<T>.Failure("请求超时", "TIMEOUT");
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "{Operation} JSON解析错误: {Url}", operation, url);
            return ServiceResult<T>.Failure("响应格式错误", "JSON_ERROR");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "{Operation} 意外错误: {Url}", operation, url);
            return ServiceResult<T>.Failure($"{operation}时发生错误, ErrorCodes.INTERNAL_ERROR");
        }
    }

    public static async Task<ServiceResult<T>> GetJsonAsync<T>(HttpClient httpClient, string url, ILogger logger, string operation = "HTTP请求")
    {
        try
        {
            logger.LogDebug("调用 {Operation}: {Url}", operation, url);

            var response = await httpClient.GetAsync(url);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogWarning("{Operation} 失败: {StatusCode} {Content}", operation, response.StatusCode, responseContent);
                return ServiceResult<T>.Failure($"{operation}失败", "HTTP_ERROR");
            }

            var result = JsonSerializer.Deserialize<T>(responseContent, JsonHelper.GetCamelCaseOptions());
            return result != null ? ServiceResult<T>.Success(result) : ServiceResult<T>.Failure("响应解析失败", "PARSE_ERROR");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "{Operation} 错误: {Url}", operation, url);
            return ServiceResult<T>.Failure($"{operation}时发生错误, ErrorCodes.INTERNAL_ERROR");
        }
    }
}
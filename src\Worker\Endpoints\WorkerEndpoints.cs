using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Shared.Common;
using Worker.Services;

namespace Worker.Endpoints;

public static class WorkerEndpoints
{
    public static void MapWorkerEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/worker").WithTags("Worker");

        group.MapPost("/fetch/{type}", async (string type, YouTubeService youTubeService, HttpContext context) =>
        {
            var body = await context.Request.ReadFromJsonAsync<JsonElement>();

            return type.ToLower() switch
            {
                "video" => await HandleVideoRequest(youTubeService, body),
                "playlist" => await HandlePlaylistRequest(youTubeService, body),
                "channel" => await HandleChannelRequest(youTubeService, body),
                _ => Results.BadRequest(ServiceResult<object>.Failure("无效的类�?, "INVALID_TYPE"))
            };
        }).WithSummary("获取YouTube内容信息");

        group.MapGet("/status", async (MetricsService metricsService, YtDlpService ytDlpService, YouTubeService youTubeService) =>
        {
            var metrics = await metricsService.GetSystemMetricsAsync();
            var ytDlpAvailable = await ytDlpService.IsYtDlpAvailableAsync();
            var providersHealth = await youTubeService.GetProvidersHealthAsync();

            var status = new
            {
                WorkerNodeId = Environment.MachineName,
                Health = ytDlpAvailable ? "健康" : "不健�?,
                YtDlpAvailable = ytDlpAvailable,
                Metrics = metrics,
                YouTubeProviders = new
                {
                    Health = providersHealth,
                    Info = youTubeService.GetProvidersInfo(),
                    Stats = youTubeService.GetUsageStats()
                },
                Timestamp = DateTime.UtcNow
            };

            return Results.Ok(ServiceResult<object>.Success(status));
        }).WithSummary("获取工作节点完整状�?);

        // 文件下载端点
        group.MapGet("/download", (string signature, string filePath, Guid taskId, Guid userId, long timestamp, IConfiguration configuration, ILogger logger) =>
        {
            try
            {
                // 验证签名
                var secretKey = configuration["FileDownload:SecretKey"] ?? "default-secret-key-change-in-production";
                var expectedSignature = GenerateFileSignature(filePath, taskId, userId, timestamp, secretKey);

                if (signature != expectedSignature)
                {
                    logger.LogWarning("文件下载签名无效: {FilePath}", filePath);
                    return Results.Unauthorized();
                }

                // 验证时间�?                var expirationTime = DateTimeOffset.FromUnixTimeSeconds(timestamp);
                if (DateTimeOffset.UtcNow > expirationTime)
                {
                    logger.LogWarning("文件下载链接已过�? {FilePath}", filePath);
                    return Results.BadRequest("下载链接已过�?);
                }

                // 检查文件是否存�?                if (!File.Exists(filePath))
                {
                    logger.LogWarning("文件不存�? {FilePath}", filePath);
                    return Results.NotFound("文件不存�?);
                }

                // 获取文件信息
                var fileInfo = new FileInfo(filePath);
                var fileName = Path.GetFileName(filePath);
                var contentType = GetFileContentType(filePath);

                logger.LogInformation("提供文件下载服务: {FileName}，任�? {TaskId}", fileName, taskId);

                // 返回文件�?                var fileStream = File.OpenRead(filePath);
                return Results.File(fileStream, contentType, fileName, enableRangeProcessing: true);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "文件下载服务发生错误: {FilePath}", filePath);
                return Results.Problem("文件下载失败");
            }
        }).WithSummary("下载文件").ExcludeFromDescription();

        // 文件删除端点（内部使用）
        group.MapPost("/delete-file", (DeleteFileRequest request, ILogger<Program> logger) =>
        {
            try
            {
                if (string.IsNullOrEmpty(request.FilePath)) return Results.BadRequest("文件路径不能为空");

                if (!File.Exists(request.FilePath))
                {
                    logger.LogWarning("要删除的文件不存�? {FilePath}", request.FilePath);
                    return Results.Ok(ServiceResult<object>.Success("文件不存在，可能已被删除"));
                }

                File.Delete(request.FilePath);
                logger.LogInformation("成功删除文件: {FilePath}", request.FilePath);

                return Results.Ok(ServiceResult<object>.Success("文件删除成功"));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "删除文件时发生错�? {FilePath}", request.FilePath);
                return Results.Problem("文件删除失败");
            }
        }).WithSummary("删除文件").ExcludeFromDescription();

        // 内部API端点
        var internalGroup = app.MapGroup("/internal").WithTags("Internal");

        internalGroup.MapGet("/health", async (HealthCheckService healthCheckService) =>
        {
            var healthResult = await healthCheckService.PerformHealthCheckAsync();

            return healthResult.Status switch
            {
                HealthStatus.Healthy => Results.Ok(ServiceResult<object>.Success(healthResult)),
                HealthStatus.Warning => Results.Ok(ServiceResult<object>.Success(healthResult)),
                HealthStatus.Critical => Results.Problem("服务不健�?, statusCode: 503),
                _ => Results.Problem("健康检查失�?, statusCode: 500)
            };
        }).WithSummary("详细健康检�?);

        internalGroup.MapGet("/metrics", async (MetricsService metricsService) =>
        {
            var metrics = await metricsService.GetSystemMetricsAsync();
            return Results.Ok(ServiceResult<object>.Success(metrics));
        }).WithSummary("获取基础系统指标");

        internalGroup.MapPost("/restart", (IHostApplicationLifetime lifetime, ILogger<Program> logger) =>
        {
            try
            {
                logger.LogInformation("收到重启命令，开始优雅关闭工作节�?);

                // 延迟一小段时间让响应返�?                _ = Task.Run(async () =>
                {
                    await Task.Delay(1000); // 等待1秒让响应返回
                    logger.LogInformation("开始停止应用程�?);
                    lifetime.StopApplication();
                });

                return Results.Ok(ServiceResult<object>.Success(new
                {
                    Message = "重启命令已接收，工作节点将在1秒后开始关�?,
                    Timestamp = DateTime.UtcNow,
                    NodeId = Environment.MachineName
                }));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "处理重启命令时发生错�?);
                return Results.BadRequest(ServiceResult<object>.Failure("重启命令处理失败", "RESTART_ERROR"));
            }
        }).WithSummary("重启工作节点");
    }

    private static string GenerateFileSignature(string filePath, Guid taskId, Guid userId, long timestamp, string secretKey)
    {
        var data = $"{filePath}|{taskId}|{userId}|{timestamp}";
        var keyBytes = Encoding.UTF8.GetBytes(secretKey);
        var dataBytes = Encoding.UTF8.GetBytes(data);

        using var hmac = new HMACSHA256(keyBytes);
        var hashBytes = hmac.ComputeHash(dataBytes);
        return Convert.ToBase64String(hashBytes);
    }

    private static string GetFileContentType(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension switch
        {
            ".mp4" => "video/mp4",
            ".webm" => "video/webm",
            ".mkv" => "video/x-matroska",
            ".mp3" => "audio/mpeg",
            ".m4a" => "audio/mp4",
            ".wav" => "audio/wav",
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".txt" => "text/plain",
            ".srt" => "text/plain",
            ".vtt" => "text/vtt",
            _ => "application/octet-stream"
        };
    }

    private static async Task<IResult> HandleVideoRequest(YouTubeService youTubeService, JsonElement body)
    {
        var request = body.Deserialize<FetchVideoRequest>()!;
        var result = await youTubeService.FetchVideoAsync(request.VideoId, request.Proxy);
        return result.IsSuccess ? Results.Ok(result) : Results.BadRequest(result);
    }

    private static async Task<IResult> HandlePlaylistRequest(YouTubeService youTubeService, JsonElement body)
    {
        var request = body.Deserialize<FetchPlaylistRequest>()!;
        var result = await youTubeService.FetchPlaylistAsync(request.PlaylistId, request.Proxy, request.MaxVideos);
        return result.IsSuccess ? Results.Ok(result) : Results.BadRequest(result);
    }

    private static async Task<IResult> HandleChannelRequest(YouTubeService youTubeService, JsonElement body)
    {
        var request = body.Deserialize<FetchChannelRequest>()!;
        var result = await youTubeService.FetchChannelAsync(request.ChannelId, request.Proxy, request.MaxVideos);
        return result.IsSuccess ? Results.Ok(result) : Results.BadRequest(result);
    }
}

public record DeleteFileRequest(string FilePath);
using System.Text.Json;
using Api.Data;
using Api.Data.Models;
using Api.Extensions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

/// <summary>
///     批量任务服务 - 专门处理批量任务
///     严格按照代码规范要求实现
/// </summary>
public class BatchTaskService
{
    private readonly IValidator<CreateBatchTaskRequest> _batchTaskValidator;
    private readonly AppDbContext _dbContext;
    private readonly ILogger<BatchTaskService> _logger;
    private readonly TaskPublishService _taskPublishService;
    private readonly YouTubeService _youTubeService;

    public BatchTaskService(AppDbContext dbContext, IValidator<CreateBatchTaskRequest> batchTaskValidator, TaskPublishService taskPublishService,
        YouTubeService youTubeService, ILogger<BatchTaskService> logger)
    {
        _dbContext = dbContext;
        _batchTaskValidator = batchTaskValidator;
        _taskPublishService = taskPublishService;
        _youTubeService = youTubeService;
        _logger = logger;
    }

    /// <summary>
    ///     创建批量任务
    /// </summary>
    public async Task<ServiceResult<BatchTaskResponse>> CreateBatchTaskAsync(CreateBatchTaskRequest request, Guid userId)
    {
        try
        {
            // 1. DTO验证
            var validationResult = await _batchTaskValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => new ValidationError(e.PropertyName, e.ErrorMessage)).ToList();
                return ServiceResult<BatchTaskResponse>.ValidationFailure(errors);
            }

            // 2. 业务规则验证
            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
                return ServiceResult<BatchTaskResponse>.Failure("用户不存在", ErrorCodes.USER_NOT_FOUND);

            if (user.Status != UserAccountStatus.Active)
                return ServiceResult<BatchTaskResponse>.Failure("用户已被禁用", ErrorCodes.USER_DISABLED);

            // 检查批量任务配额
            var userBatchTaskCount = await _dbContext.BatchTasks
                .Where(t => t.UserId == userId && t.Status != BatchTaskStatus.Completed && t.Status != BatchTaskStatus.Failed).CountAsync();

            var maxBatchTasks = user.PlanType == UserPlanType.Premium ? 10 : 2;
            if (userBatchTaskCount >= maxBatchTasks)
                return ServiceResult<BatchTaskResponse>.Failure("批量任务配额已满", ErrorCodes.USER_QUOTA_EXCEEDED);

            // 3. 创建批量任务实体
            var batchTask = request.ToEntity(userId);

            // 4. 解析配置并创建子任务
            var configuration = JsonSerializer.Deserialize<BatchTaskConfiguration>(request.Configuration);
            if (configuration == null)
                return ServiceResult<BatchTaskResponse>.Failure("配置信息格式错误", ErrorCodes.INVALID_ARGUMENT);

            var workerTasks = new List<WorkerTask>();
            foreach (var videoId in request.SelectedVideoIds)
            {
                var workerTask = new WorkerTask
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    BatchTaskId = batchTask.Id,
                    Name = $"批量任务 - {videoId}",
                    TaskType = configuration.TaskType,
                    VideoId = videoId,
                    Parameters = JsonSerializer.Serialize(configuration),
                    Status = WorkerTaskStatus.Pending,
                    Progress = 0,
                    Priority = WorkerTaskPriority.Normal,
                    RetryCount = 0,
                    OutputFormat = configuration.OutputFormat,
                    Quality = configuration.Quality,
                    StartTime = configuration.StartTime,
                    EndTime = configuration.EndTime,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                workerTasks.Add(workerTask);
            }

            // 5. 使用事务保证数据一致性
            using var transaction = await _dbContext.Database.BeginTransactionAsync();
            try
            {
                _dbContext.BatchTasks.Add(batchTask);
                _dbContext.WorkerTasks.AddRange(workerTasks);
                await _dbContext.SaveChangesAsync();

                // 6. 发布子任务到消息队列
                foreach (var task in workerTasks) await _taskPublishService.PublishTaskAsync(task);

                await transaction.CommitAsync();

                _logger.LogInformation("用户 {UserId} 创建批量任务 {BatchTaskId} 成功，包含 {TaskCount} 个子任务", userId, batchTask.Id, workerTasks.Count);

                // 7. 返回响应
                batchTask.WorkerTasks = workerTasks;
                return ServiceResult<BatchTaskResponse>.Success(batchTask.ToResponse());
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建批量任务失败，用户ID: {UserId}", userId);
            return ServiceResult<BatchTaskResponse>.Failure("创建批量任务失败", ErrorCodes.INTERNAL_ERROR);
        }
    }

    /// <summary>
    ///     获取用户批量任务列表
    /// </summary>
    public async Task<ServiceResult<List<BatchTaskResponse>>> GetUserBatchTasksAsync(Guid userId, int skip = 0, int take = 20)
    {
        try
        {
            var batchTasks = await _dbContext.BatchTasks.AsNoTracking().Include(bt => bt.WorkerTasks).Where(bt => bt.UserId == userId)
                .OrderByDescending(bt => bt.CreatedAt).Skip(skip).Take(take).ToListAsync();

            var responses = batchTasks.Select(bt => bt.ToResponse()).ToList();

            return ServiceResult<List<BatchTaskResponse>>.Success(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户批量任务列表失败，用户ID: {UserId}", userId);
            return ServiceResult<List<BatchTaskResponse>>.Failure("获取批量任务列表失败", ErrorCodes.INTERNAL_ERROR);
        }
    }

    /// <summary>
    ///     获取批量任务详情
    /// </summary>
    public async Task<ServiceResult<BatchTaskResponse>> GetBatchTaskAsync(Guid batchTaskId, Guid userId)
    {
        try
        {
            var batchTask = await _dbContext.BatchTasks.AsNoTracking().Include(bt => bt.WorkerTasks)
                .FirstOrDefaultAsync(bt => bt.Id == batchTaskId && bt.UserId == userId);

            if (batchTask == null)
                return ServiceResult<BatchTaskResponse>.Failure("批量任务不存在", ErrorCodes.TASK_NOT_FOUND);

            return ServiceResult<BatchTaskResponse>.Success(batchTask.ToResponse());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取批量任务详情失败，任务ID: {BatchTaskId}", batchTaskId);
            return ServiceResult<BatchTaskResponse>.Failure("获取批量任务详情失败", ErrorCodes.INTERNAL_ERROR);
        }
    }

    /// <summary>
    ///     取消批量任务
    /// </summary>
    public async Task<ServiceResult> CancelBatchTaskAsync(Guid batchTaskId, Guid userId)
    {
        try
        {
            var batchTask = await _dbContext.BatchTasks.Include(bt => bt.WorkerTasks).FirstOrDefaultAsync(bt => bt.Id == batchTaskId && bt.UserId == userId);

            if (batchTask == null)
                return ServiceResult.Failure("批量任务不存在", ErrorCodes.TASK_NOT_FOUND);

            if (batchTask.Status == BatchTaskStatus.Completed)
                return ServiceResult.Failure("批量任务已完成，无法取消", ErrorCodes.TASK_ALREADY_COMPLETED);

            if (batchTask.Status == BatchTaskStatus.Cancelled)
                return ServiceResult.Failure("批量任务已取消", ErrorCodes.TASK_CANCELLED);

            // 取消批量任务和所有未完成的子任务
            batchTask.Status = BatchTaskStatus.Cancelled;
            batchTask.UpdatedAt = DateTime.UtcNow;

            foreach (var workerTask in batchTask.WorkerTasks)
                if (workerTask.Status == WorkerTaskStatus.Pending || workerTask.Status == WorkerTaskStatus.Queued)
                {
                    workerTask.Status = WorkerTaskStatus.Cancelled;
                    workerTask.UpdatedAt = DateTime.UtcNow;
                    batchTask.CancelledTaskCount++;
                }

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("用户 {UserId} 取消批量任务 {BatchTaskId} 成功", userId, batchTaskId);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消批量任务失败，任务ID: {BatchTaskId}", batchTaskId);
            return ServiceResult.Failure("取消批量任务失败", ErrorCodes.INTERNAL_ERROR);
        }
    }

    /// <summary>
    ///     更新批量任务进度
    ///     当子任务状态变化时调用
    /// </summary>
    public async Task<ServiceResult> UpdateBatchTaskProgressAsync(Guid batchTaskId)
    {
        try
        {
            var batchTask = await _dbContext.BatchTasks.Include(bt => bt.WorkerTasks).FirstOrDefaultAsync(bt => bt.Id == batchTaskId);

            if (batchTask == null)
                return ServiceResult.Failure("批量任务不存在", ErrorCodes.TASK_NOT_FOUND);

            // 计算进度
            var totalTasks = batchTask.WorkerTasks.Count;
            var completedTasks = batchTask.WorkerTasks.Count(t => t.Status == WorkerTaskStatus.Completed);
            var failedTasks = batchTask.WorkerTasks.Count(t => t.Status == WorkerTaskStatus.Failed);
            var cancelledTasks = batchTask.WorkerTasks.Count(t => t.Status == WorkerTaskStatus.Cancelled);

            batchTask.CompletedTaskCount = completedTasks;
            batchTask.FailedTaskCount = failedTasks;
            batchTask.CancelledTaskCount = cancelledTasks;

            // 计算总进度
            var totalProgress = batchTask.WorkerTasks.Sum(t => t.Progress);
            batchTask.Progress = totalTasks > 0 ? totalProgress / totalTasks : 0;

            // 更新状态
            if (completedTasks + failedTasks + cancelledTasks == totalTasks)
            {
                batchTask.Status = completedTasks > 0 ? BatchTaskStatus.Completed : BatchTaskStatus.Failed;
                batchTask.CompletedAt = DateTime.UtcNow;
            }
            else if (batchTask.Status == BatchTaskStatus.Created)
            {
                batchTask.Status = BatchTaskStatus.Running;
                batchTask.StartedAt = DateTime.UtcNow;
            }

            batchTask.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新批量任务进度失败，任务ID: {BatchTaskId}", batchTaskId);
            return ServiceResult.Failure("更新批量任务进度失败", ErrorCodes.INTERNAL_ERROR);
        }
    }
}

/// <summary>
///     批量任务配置
/// </summary>
public class BatchTaskConfiguration
{
    public WorkerTaskType TaskType { get; set; }
    public string? OutputFormat { get; set; }
    public string? Quality { get; set; }
    public int? StartTime { get; set; }
    public int? EndTime { get; set; }
    public int? Fps { get; set; }
    public int? Width { get; set; }
}
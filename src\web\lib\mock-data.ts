import type {YouTubeVideoInfo} from './types';

export const mockVideoData: YouTubeVideoInfo = {
    videoId: 'dQw4w9WgXcQ',
    title: '<PERSON> - Never Gonna Give You Up (Official Video)',
    description: `The official video for "Never Gonna Give You Up" by <PERSON>

"Never Gonna Give You Up" was a global smash on its release in July 1987, topping the charts in 25 countries including <PERSON>'s native UK and the US Billboard Hot 100.  It also won the Brit Award for Best single in 1988. <PERSON> and <PERSON> wrote and produced the track which was the lead-off single and lead track from <PERSON>'s debut LP "Whenever You Need Somebody".

The album was itself a UK number one and would go on to sell over 15 million copies worldwide.

The legendary video was directed by <PERSON> who later went on to make Hollywood blockbusters such as <PERSON>, <PERSON> – <PERSON> Raider and The Expendables 2.  The video passed the 1 billion views milestone on 28 July 2021.`,
    duration: '3:33',
    durationSeconds: 213,
    viewCount: 1500000000,
    likeCount: 15000000,
    commentCount: 2500000,
    publishedAt: '2009-10-25T06:57:33Z',
    channelId: 'UCuAXFkgsw1L7xaCfnd5JJOw',
    channelTitle: '<PERSON>',
    channelUrl: 'https://www.youtube.com/channel/UCuAXFkgsw1L7xaCfnd5JJOw',
    thumbnails: {
        default: 'https://img.youtube.com/vi/dQw4w9WgXcQ/default.jpg',
        medium: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        high: 'https://img.youtube.com/vi/dQw4w9WgXcQ/hqdefault.jpg',
        maxres: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    },
    formats: [
        {
            formatId: '137',
            ext: 'mp4',
            resolution: '1080p',
            width: 1920,
            height: 1080,
            fps: 30,
            vcodec: 'avc1.640028',
            acodec: 'none',
            filesize: 167000000,
            quality: 'hd1080',
            hasAudio: false,
            hasVideo: true,
        },
        {
            formatId: '136',
            ext: 'mp4',
            resolution: '720p',
            width: 1280,
            height: 720,
            fps: 30,
            vcodec: 'avc1.4d401f',
            acodec: 'none',
            filesize: 89000000,
            quality: 'hd720',
            hasAudio: false,
            hasVideo: true,
        },
        {
            formatId: '135',
            ext: 'mp4',
            resolution: '480p',
            width: 854,
            height: 480,
            fps: 30,
            vcodec: 'avc1.4d401e',
            acodec: 'none',
            filesize: 45000000,
            quality: 'large',
            hasAudio: false,
            hasVideo: true,
        },
        {
            formatId: '18',
            ext: 'mp4',
            resolution: '360p',
            width: 640,
            height: 360,
            fps: 30,
            vcodec: 'avc1.42001E',
            acodec: 'mp4a.40.2',
            filesize: 25000000,
            quality: 'medium',
            hasAudio: true,
            hasVideo: true,
        },
    ],
    audioFormats: [
        {
            formatId: '140',
            ext: 'm4a',
            acodec: 'mp4a.40.2',
            abr: 128,
            filesize: 3400000,
            quality: 'medium',
        },
        {
            formatId: '251',
            ext: 'webm',
            acodec: 'opus',
            abr: 160,
            filesize: 4200000,
            quality: 'medium',
        },
        {
            formatId: '250',
            ext: 'webm',
            acodec: 'opus',
            abr: 70,
            filesize: 1800000,
            quality: 'low',
        },
    ],
    subtitles: [
        {
            language: 'English',
            languageCode: 'en',
            isAutoGenerated: false,
            formats: ['vtt', 'srt'],
        },
        {
            language: 'Spanish',
            languageCode: 'es',
            isAutoGenerated: true,
            formats: ['vtt', 'srt'],
        },
        {
            language: 'French',
            languageCode: 'fr',
            isAutoGenerated: true,
            formats: ['vtt', 'srt'],
        },
    ],
    isLive: false,
    isUpcoming: false,
    ageRestricted: false,
    availability: 'public',
};

export function getMockVideoData(videoId: string): YouTubeVideoInfo {
    return {
        ...mockVideoData,
        videoId,
        title: `示例视频 - ${videoId}`,
        description: `这是视频 ${videoId} 的示例描述。这是一个用于测试的模拟数据。`,
    };
}

export const shouldUseMockData = (): boolean => {
    return process.env.NEXT_PUBLIC_ENV === 'development' &&
        process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true';
};

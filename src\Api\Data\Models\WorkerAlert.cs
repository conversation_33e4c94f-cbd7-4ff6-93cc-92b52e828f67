﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.Common;

namespace Api.Data.Models;

public class WorkerAlert
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public required Guid WorkerId { get; set; }
    public required WorkerAlertType AlertType { get; set; }
    public required WorkerAlertLevel AlertLevel { get; set; }
    public required string Title { get; set; }
    public required string Message { get; set; }
    public bool IsResolved { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public DateTime CreatedAt { get; init; }

    public Worker Worker { get; set; } = null!;
}

public class WorkerAlertConfiguration : IEntityTypeConfiguration<WorkerAlert>
{
    public void Configure(EntityTypeBuilder<WorkerAlert> builder)
    {
        builder.ToTable("worker_alerts", "public");
        builder.HasKey(a => a.Id);
        builder.Property(a => a.Id).ValueGeneratedNever();
        builder.Property(a => a.WorkerId).IsRequired().HasColumnName("worker_id");
        builder.Property(a => a.AlertType).IsRequired().HasColumnName("alert_type").HasConversion<string>();
        builder.Property(a => a.AlertLevel).IsRequired().HasColumnName("alert_level").HasConversion<string>();
        builder.Property(a => a.Title).IsRequired().HasMaxLength(200).HasColumnName("title");
        builder.Property(a => a.Message).IsRequired().HasColumnName("message");
        builder.Property(a => a.IsResolved).IsRequired().HasColumnName("is_resolved").HasDefaultValue(false);
        builder.Property(a => a.ResolvedAt).HasColumnName("resolved_at");
        builder.Property(a => a.CreatedAt).IsRequired().HasColumnName("created_at").ValueGeneratedOnAdd();

        builder.HasIndex(a => a.WorkerId).HasDatabaseName("ix_worker_alerts_worker_id");
        builder.HasIndex(a => a.CreatedAt).HasDatabaseName("ix_worker_alerts_created_at");
        builder.HasIndex(a => a.IsResolved).HasDatabaseName("ix_worker_alerts_is_resolved");
        builder.HasIndex(a => new { a.WorkerId, a.IsResolved }).HasDatabaseName("ix_worker_alerts_worker_resolved");
        builder.HasIndex(a => new { a.AlertLevel, a.IsResolved, a.CreatedAt }).HasDatabaseName("ix_worker_alerts_level_resolved_created");

        builder.HasOne(a => a.Worker).WithMany(w => w.Alerts).HasForeignKey(a => a.WorkerId).OnDelete(DeleteBehavior.Cascade);
    }
}
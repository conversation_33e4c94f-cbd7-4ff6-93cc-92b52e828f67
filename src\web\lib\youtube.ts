export interface YouTubeLinkInfo {
    type: 'video' | 'playlist' | 'channel' | 'invalid';
    id: string;
    originalUrl: string;
    isValid: boolean;
}

export interface YouTubeValidationResult {
    isValid: boolean;
    type?: 'video' | 'playlist' | 'channel';
    id?: string;
    error?: string;
}

const VIDEO_PATTERNS = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/shorts\/([a-zA-Z0-9_-]{11})/,
];

const PLAYLIST_PATTERNS = [
    /youtube\.com\/playlist\?list=([a-zA-Z0-9_-]+)/,
    /youtube\.com\/watch\?.*list=([a-zA-Z0-9_-]+)/,
];

const CHANNEL_PATTERNS = [
    /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/c\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/user\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/@([a-zA-Z0-9_.-]+)/,
];

export function validateYouTubeUrl(url: string): YouTubeValidationResult {
    if (!url || typeof url !== 'string') {
        return {isValid: false, error: '请输入有效的URL'};
    }

    const trimmedUrl = url.trim();
    if (!trimmedUrl) {
        return {isValid: false, error: '链接不能为空'};
    }

    if (!trimmedUrl.includes('youtube.com') && !trimmedUrl.includes('youtu.be')) {
        return {isValid: false, error: '请输入YouTube链接'};
    }

    for (const pattern of VIDEO_PATTERNS) {
        const match = trimmedUrl.match(pattern);
        if (match) {
            return {
                isValid: true,
                type: 'video',
                id: match[1],
            };
        }
    }

    for (const pattern of PLAYLIST_PATTERNS) {
        const match = trimmedUrl.match(pattern);
        if (match) {
            return {
                isValid: true,
                type: 'playlist',
                id: match[1],
            };
        }
    }

    for (const pattern of CHANNEL_PATTERNS) {
        const match = trimmedUrl.match(pattern);
        if (match) {
            return {
                isValid: true,
                type: 'channel',
                id: match[1],
            };
        }
    }

    return {isValid: false, error: '无法识别的YouTube链接格式'};
}

export function parseYouTubeUrl(url: string): YouTubeLinkInfo {
    const validation = validateYouTubeUrl(url);

    return {
        type: validation.isValid ? validation.type! : 'invalid',
        id: validation.id || '',
        originalUrl: url,
        isValid: validation.isValid,
    };
}

export function validateMultipleYouTubeUrls(urls: string[]): {
    validUrls: YouTubeLinkInfo[];
    invalidUrls: string[];
    totalCount: number;
    validCount: number;
} {
    const validUrls: YouTubeLinkInfo[] = [];
    const invalidUrls: string[] = [];

    urls.forEach(url => {
        const trimmedUrl = url.trim();
        if (!trimmedUrl) return;

        const linkInfo = parseYouTubeUrl(trimmedUrl);
        if (linkInfo.isValid && linkInfo.type === 'video') {
            validUrls.push(linkInfo);
        } else {
            invalidUrls.push(trimmedUrl);
        }
    });

    return {
        validUrls,
        invalidUrls,
        totalCount: urls.filter(url => url.trim()).length,
        validCount: validUrls.length,
    };
}

export function extractVideoIdFromUrl(url: string): string | null {
    const validation = validateYouTubeUrl(url);
    return validation.isValid && validation.type === 'video' ? validation.id! : null;
}

export function generateVideoUrl(videoId: string): string {
    return `https://www.youtube.com/watch?v=${videoId}`;
}

export function generateThumbnailUrl(videoId: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'medium'): string {
    const qualityMap = {
        default: 'default',
        medium: 'mqdefault',
        high: 'hqdefault',
        maxres: 'maxresdefault',
    };

    return `https://img.youtube.com/vi/${videoId}/${qualityMap[quality]}.jpg`;
}

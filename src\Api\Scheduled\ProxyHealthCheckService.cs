using Api.Services;

namespace Api.Scheduled;

public class ProxyHealthCheckService(IServiceProvider serviceProvider, ILogger<ProxyHealthCheckService> logger) : BackgroundService
{
    private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(10);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("代理健康检查服务已启动");

        while (!stoppingToken.IsCancellationRequested)
            try
            {
                await PerformHealthCheckAsync();
                await Task.Delay(_checkInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "代理健康检查循环中发生错误");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }

        logger.LogInformation("代理健康检查服务已停止");
    }

    private async Task PerformHealthCheckAsync()
    {
        using var scope = serviceProvider.CreateScope();
        var proxyService = scope.ServiceProvider.GetRequiredService<ProxyService>();

        try
        {
            logger.LogDebug("开始代理健康检查");

            using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));
            var result = await proxyService.HealthCheckAllProxiesAsync(cts.Token);

            if (result.IsSuccess)
                logger.LogInformation("代理健康检查完成。{HealthyCount} 个代理健康", result.Data);
            else
                logger.LogWarning("代理健康检查失败: {Error}", result.ErrorMessage);
        }
        catch (OperationCanceledException)
        {
            logger.LogWarning("代理健康检查因超时被取消");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "执行代理健康检查时发生错误");
        }
    }
}
using System.Globalization;
using System.Text.Json;

namespace Worker.Helpers;

public static class JsonHelper
{
    public static T? TryGetProperty<T>(JsonElement element, string propertyName, T? defaultValue = default)
    {
        if (!element.TryGetProperty(propertyName, out var property))
            return defaultValue;

        try
        {
            return typeof(T) switch
            {
                var t when t == typeof(string) => (T?)(object?)property.GetString(),
                var t when t == typeof(int) => (T?)(object?)property.GetInt32(),
                var t when t == typeof(long) => (T?)(object?)property.GetInt64(),
                var t when t == typeof(float) => (T?)(object?)property.GetSingle(),
                var t when t == typeof(double) => (T?)(object?)property.GetDouble(),
                var t when t == typeof(bool) => (T?)(object?)property.GetBoolean(),
                var t when t == typeof(DateTime) => (T?)(object?)property.GetDateTime(),
                _ => defaultValue
            };
        }
        catch
        {
            return defaultValue;
        }
    }

    public static DateTime? ParseYouTubeDate(JsonElement element, string propertyName)
    {
        if (!element.TryGetProperty(propertyName, out var property) || property.GetString() is not string dateStr)
            return null;

        return DateTime.TryParseExact(dateStr, "yyyyMMdd", null, DateTimeStyles.None, out var date) ? date : null;
    }

    public static JsonSerializerOptions GetCamelCaseOptions()
    {
        return new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }
}
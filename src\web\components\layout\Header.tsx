import Link from "next/link";
import {Download} from "lucide-react";

export function Header() {
    return (
        <header className="border-b">
            <div className="container mx-auto px-4 py-4">
                <div className="flex items-center justify-between">
                    <Link href="/" className="flex items-center space-x-2">
                        <Download className="h-6 w-6"/>
                        <span className="text-xl font-bold">YouTube Downloader</span>
                    </Link>

                    <nav className="hidden md:flex items-center space-x-6">
                        <Link
                            href="/pricing"
                            className="text-sm font-medium hover:text-primary transition-colors"
                        >
                            价格
                        </Link>
                        <Link
                            href="/privacy"
                            className="text-sm font-medium hover:text-primary transition-colors"
                        >
                            隐私政策
                        </Link>
                    </nav>

                    <div className="md:hidden">
                        <span className="text-sm text-muted-foreground">菜单</span>
                    </div>
                </div>
            </div>
        </header>
    );
}

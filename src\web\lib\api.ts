interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}

class ApiClient {
    private baseURL: string;

    constructor() {
        this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
    }

    async get<T>(endpoint: string): Promise<ApiResponse<T>> {
        return this.request<T>(endpoint, {method: 'GET'});
    }

    async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
        return this.request<T>(endpoint, {
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined,
        });
    }

    private async request<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<ApiResponse<T>> {
        const url = `${this.baseURL}${endpoint}`;

        const config: RequestInit = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
            credentials: 'include',
            ...options,
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                return {
                    success: false,
                    error: data.message || `HTTP error! status: ${response.status}`,
                };
            }

            return {
                success: true,
                data: data.data || data,
                message: data.message,
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '网络请求失败',
            };
        }
    }
}

export const apiClient = new ApiClient();

// YouTube API 方法
export const youtubeApi = {
    parseUrl: (url: string) =>
        apiClient.post('/api/youtube/parse-url', {url}),

    getVideoInfo: (videoId: string) =>
        apiClient.get(`/api/youtube/video/${videoId}`),

    getPlaylistInfo: (playlistId: string) =>
        apiClient.get(`/api/youtube/playlist/${playlistId}`),

    getChannelInfo: (channelId: string) =>
        apiClient.get(`/api/youtube/channel/${channelId}`),

    fetchContent: (urls: string[]) =>
        apiClient.post('/api/youtube/fetch', {urls}),
};

// 任务 API 方法
export const taskApi = {
    createBatchTask: (data: { urls: string[]; config?: any }) =>
        apiClient.post('/api/tasks/batch', data),

    getTaskList: () =>
        apiClient.get('/api/tasks'),

    getTaskDetail: (taskId: string) =>
        apiClient.get(`/api/tasks/${taskId}`),

    cancelTask: (taskId: string) =>
        apiClient.post(`/api/tasks/${taskId}/cancel`),

    retryTask: (taskId: string) =>
        apiClient.post(`/api/tasks/${taskId}/retry`),
};

// 认证 API 方法
export const authApi = {
    login: (email: string, password: string) =>
        apiClient.post('/api/auth/login', {email, password}),

    register: (email: string, password: string) =>
        apiClient.post('/api/auth/register', {email, password}),

    logout: () =>
        apiClient.post('/api/auth/logout'),

    checkAuth: () =>
        apiClient.get('/api/auth/check'),

    getCurrentUser: () =>
        apiClient.get('/api/auth/me'),
};

export type {ApiResponse};

using FluentValidation;
using Shared.Common;

namespace Shared.DTOs;

public record ProxyInfo(string Host, int Port, string? Username = null, string? Password = null, ProxyType Type = ProxyType.Http);

public record AddProxyRequest(string Host, int Port, string? Username, string? Password, ProxyType Type, string? Notes);

public class AddProxyRequestValidator : AbstractValidator<AddProxyRequest>
{
    public AddProxyRequestValidator()
    {
        RuleFor(x => x.Host).NotEmpty().WithMessage("代理主机不能为空").MaximumLength(255).WithMessage("代理主机长度不能超过255个字符");

        RuleFor(x => x.Port).InclusiveBetween(1, 65535).WithMessage("端口必须在1-65535之间");

        RuleFor(x => x.Username).MaximumLength(100).WithMessage("用户名长度不能超过100个字符");

        RuleFor(x => x.Password).MaximumLength(255).WithMessage("密码长度不能超过255个字符");

        RuleFor(x => x.Type).IsInEnum().WithMessage("代理类型无效");

        RuleFor(x => x.Notes).MaximumLength(500).WithMessage("备注长度不能超过500个字符");
    }
}

public record BatchAddProxyRequest(List<AddProxyRequest> Proxies);

public class BatchAddProxyRequestValidator : AbstractValidator<BatchAddProxyRequest>
{
    public BatchAddProxyRequestValidator()
    {
        RuleFor(x => x.Proxies).NotEmpty().WithMessage("代理列表不能为空").Must(x => x.Count <= 100).WithMessage("单次最多添加100个代理");

        RuleForEach(x => x.Proxies).SetValidator(new AddProxyRequestValidator());
    }
}

public record UpdateProxyRequest(Guid ProxyId, ProxyStatus? Status, string? Notes);

public class UpdateProxyRequestValidator : AbstractValidator<UpdateProxyRequest>
{
    public UpdateProxyRequestValidator()
    {
        RuleFor(x => x.ProxyId).NotEmpty().WithMessage("代理ID不能为空");

        RuleFor(x => x.Status).IsInEnum().When(x => x.Status.HasValue).WithMessage("代理状态无效");

        RuleFor(x => x.Notes).MaximumLength(500).WithMessage("备注长度不能超过500个字符");
    }
}

public record DeleteProxyRequest(Guid ProxyId);

public class DeleteProxyRequestValidator : AbstractValidator<DeleteProxyRequest>
{
    public DeleteProxyRequestValidator()
    {
        RuleFor(x => x.ProxyId).NotEmpty().WithMessage("代理ID不能为空");
    }
}

public record ProxyDetailResponse(
    Guid Id,
    string Host,
    int Port,
    string? Username,
    ProxyType Type,
    ProxyStatus Status,
    ProxyHealthStatus HealthStatus,
    DateTime? LastUsedAt,
    DateTime? LastHealthCheckAt,
    int? ResponseTimeMs,
    int FailureCount,
    int SuccessCount,
    int UsageCount,
    string? ErrorMessage,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    string? Notes);

public record ProxyStats(int TotalProxies, int ActiveProxies, int HealthyProxies, int UnhealthyProxies, double HealthRate, DateTime LastHealthCheck);
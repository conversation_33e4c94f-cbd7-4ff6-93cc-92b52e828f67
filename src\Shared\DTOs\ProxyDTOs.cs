using FluentValidation;
using Shared.Common;

namespace Shared.DTOs;

public record ProxyInfo(string Host, int Port, string? Username = null, string? Password = null, ProxyType Type = ProxyType.Http);

public record AddProxyRequest(string Host, int Port, string? Username, string? Password, ProxyType Type, string? Notes);

public class AddProxyRequestValidator : AbstractValidator<AddProxyRequest>
{
    public AddProxyRequestValidator()
    {
        RuleFor(x => x.Host).NotEmpty().WithMessage("代理主机不能为空").MaximumLength(255).WithMessage("代理主机长度不能超过255个字符");

        RuleFor(x => x.Port).InclusiveBetween(1, 65535).WithMessage("端口必须在1-65535之间");

        RuleFor(x => x.Username).MaximumLength(100).WithMessage("用户名长度不能超过100个字符");

        RuleFor(x => x.Password).MaximumLength(255).WithMessage("密码长度不能超过255个字符");

        RuleFor(x => x.Type).IsInEnum().WithMessage("代理类型无效");

        RuleFor(x => x.Notes).MaximumLength(500).WithMessage("备注长度不能超过500个字符");
    }
}

public record BatchAddProxyRequest(List<AddProxyRequest> Proxies);

public class BatchAddProxyRequestValidator : AbstractValidator<BatchAddProxyRequest>
{
    public BatchAddProxyRequestValidator()
    {
        RuleFor(x => x.Proxies).NotEmpty().WithMessage("代理列表不能为空").Must(x => x.Count <= 100).WithMessage("单次最多添加100个代理");

        RuleForEach(x => x.Proxies).SetValidator(new AddProxyRequestValidator());
    }
}

public record UpdateProxyRequest(string Host, int Port, string? Username, string? Password, ProxyType Type, string? Notes);

public class UpdateProxyRequestValidator : AbstractValidator<UpdateProxyRequest>
{
    public UpdateProxyRequestValidator()
    {
        RuleFor(x => x.Host).NotEmpty().WithMessage("主机地址不能为空").MaximumLength(255).WithMessage("主机地址长度不能超过255个字符");
        RuleFor(x => x.Port).InclusiveBetween(1, 65535).WithMessage("端口必须在1-65535之间");
        RuleFor(x => x.Username).MaximumLength(255).WithMessage("用户名长度不能超过255个字符");
        RuleFor(x => x.Password).MaximumLength(255).WithMessage("密码长度不能超过255个字符");
        RuleFor(x => x.Type).IsInEnum().WithMessage("代理类型无效");
        RuleFor(x => x.Notes).MaximumLength(500).WithMessage("备注长度不能超过500个字符");
    }
}



public record ProxyDetailResponse(
    Guid Id,
    string Host,
    int Port,
    string? Username,
    ProxyType Type,
    ProxyStatus Status,
    ProxyHealthStatus HealthStatus,
    DateTime? LastUsedAt,
    DateTime? LastHealthCheckAt,
    int? ResponseTimeMs,
    int FailureCount,
    int SuccessCount,
    int UsageCount,
    string? ErrorMessage,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    string? Notes);

public record ProxyStats(int TotalProxies, int ActiveProxies, int HealthyProxies, int UnhealthyProxies, double HealthRate, DateTime LastHealthCheck);

public record CreateProxyRequest(string Host, int Port, string? Username, string? Password, ProxyType Type, string? Notes);

public record BatchImportProxiesRequest(List<CreateProxyRequest> Proxies);

public record BatchDeleteProxiesRequest(List<Guid> ProxyIds);

public record ProxyListResponse(
    List<ProxyDetailResponse> Proxies,
    int TotalCount,
    int Page,
    int PageSize);

public record ProxyHealthCheckResponse(
    Guid ProxyId,
    bool IsHealthy,
    int? ResponseTimeMs,
    string? ErrorMessage,
    DateTime CheckedAt);
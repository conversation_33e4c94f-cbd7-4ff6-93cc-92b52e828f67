using System.Text.Json;
using Worker.Services;

namespace Worker.Scheduled;

public class HeartbeatService : BackgroundService
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
    };

    private readonly IConfiguration _configuration;

    private readonly TimeSpan _heartbeatInterval = TimeSpan.FromSeconds(30);
    private readonly HttpClient _httpClient;
    private readonly ILogger<HeartbeatService> _logger;
    private readonly int _maxHeartbeatFailures = 5;
    private readonly int _maxRegistrationRetries = 10;
    private readonly MetricsService _metricsService;

    private int _consecutiveHeartbeatFailures;
    private Guid? _nodeId;
    private int _registrationRetryCount;

    public HeartbeatService(ILogger<HeartbeatService> logger, IConfiguration configuration, HttpClient httpClient, MetricsService metricsService)
    {
        _logger = logger;
        _configuration = configuration;
        _httpClient = httpClient;
        _metricsService = metricsService;
    }

    public bool IsRegistered { get; private set; }

    public Guid? GetNodeId()
    {
        return _nodeId;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("节点生命周期服务已启动");

        try
        {
            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);

            if (!await PerformSelfCheckAsync())
            {
                _logger.LogError("自检失败，节点将不会注册");
                return;
            }

            await PerformRegistrationPhaseAsync(stoppingToken);

            if (IsRegistered)
                await PerformHeartbeatPhaseAsync(stoppingToken);
        }
        finally
        {
            if (IsRegistered)
                await UnregisterNodeAsync();

            _logger.LogInformation("节点生命周期服务已停止");
        }
    }

    private async Task<bool> PerformSelfCheckAsync()
    {
        _logger.LogInformation("开始执行节点自检");

        try
        {
            var coreServiceUrl = _configuration["CoreService:BaseUrl"];
            var workerBaseUrl = _configuration["Worker:BaseUrl"];
            var apiKey = _configuration["CoreService:ApiKey"];

            if (string.IsNullOrEmpty(coreServiceUrl) || string.IsNullOrEmpty(workerBaseUrl) || string.IsNullOrEmpty(apiKey))
            {
                _logger.LogError("自检失败: 关键配置缺失");
                return false;
            }

            var originalTimeout = _httpClient.Timeout;
            _httpClient.Timeout = TimeSpan.FromSeconds(10);

            try
            {
                var coreResponse = await _httpClient.GetAsync($"{coreServiceUrl}/health");
                if (!coreResponse.IsSuccessStatusCode)
                {
                    _logger.LogError("自检失败: 核心服务健康检查失败");
                    return false;
                }

                var workerResponse = await _httpClient.GetAsync($"{workerBaseUrl}/internal/health");
                if (!workerResponse.IsSuccessStatusCode)
                {
                    _logger.LogError("自检失败: 内部健康检查端点无响应");
                    return false;
                }

                _logger.LogInformation("节点自检通过");
                return true;
            }
            finally
            {
                _httpClient.Timeout = originalTimeout;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自检过程中发生错误");
            return false;
        }
    }

    private async Task PerformRegistrationPhaseAsync(CancellationToken stoppingToken)
    {
        while (!IsRegistered && !stoppingToken.IsCancellationRequested && _registrationRetryCount < _maxRegistrationRetries)
            try
            {
                _registrationRetryCount++;
                _logger.LogInformation("尝试注册节点，第 {RetryCount}/{MaxRetries} 次", _registrationRetryCount, _maxRegistrationRetries);

                await TryRegisterNodeAsync();

                if (IsRegistered)
                {
                    _logger.LogInformation("节点注册成功，重置重试计数");
                    _registrationRetryCount = 0;
                    break;
                }

                var delay = CalculateBackoffDelay(_registrationRetryCount);
                _logger.LogWarning("节点注册失败，{Delay} 秒后重试", delay.TotalSeconds);
                await Task.Delay(delay, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册阶段发生错误，第 {RetryCount}/{MaxRetries} 次", _registrationRetryCount, _maxRegistrationRetries);
                var delay = CalculateBackoffDelay(_registrationRetryCount);
                await Task.Delay(delay, stoppingToken);
            }

        if (!IsRegistered && _registrationRetryCount >= _maxRegistrationRetries)
            _logger.LogError("节点注册失败，已达到最大重试次数 {MaxRetries}，停止注册尝试", _maxRegistrationRetries);
    }

    private async Task PerformHeartbeatPhaseAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("开始心跳阶段");

        while (!stoppingToken.IsCancellationRequested)
            try
            {
                var heartbeatSuccess = await SendHeartbeatAsync();

                if (heartbeatSuccess)
                {
                    if (_consecutiveHeartbeatFailures > 0)
                    {
                        _logger.LogInformation("心跳恢复正常，重置失败计数");
                        _consecutiveHeartbeatFailures = 0;
                    }
                }
                else
                {
                    _consecutiveHeartbeatFailures++;
                    _logger.LogWarning("心跳失败，连续失败次数: {FailureCount}/{MaxFailures}", _consecutiveHeartbeatFailures, _maxHeartbeatFailures);

                    if (_consecutiveHeartbeatFailures >= _maxHeartbeatFailures)
                    {
                        _logger.LogError("心跳连续失败 {FailureCount} 次，尝试重新注册节点", _consecutiveHeartbeatFailures);
                        IsRegistered = false;
                        _nodeId = null;
                        _consecutiveHeartbeatFailures = 0;
                        _registrationRetryCount = 0;

                        await PerformRegistrationPhaseAsync(stoppingToken);

                        if (!IsRegistered)
                        {
                            _logger.LogError("重新注册失败，停止心跳服务");
                            break;
                        }
                    }
                }

                await Task.Delay(_heartbeatInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _consecutiveHeartbeatFailures++;
                _logger.LogError(ex, "心跳阶段发生错误，连续失败次数: {FailureCount}", _consecutiveHeartbeatFailures);

                var delay = Math.Min(10 + _consecutiveHeartbeatFailures * 2, 60);
                await Task.Delay(TimeSpan.FromSeconds(delay), stoppingToken);
            }
    }

    private async Task TryRegisterNodeAsync()
    {
        try
        {
            var coreServiceUrl = GetCoreServiceUrl();
            var workerBaseUrl = GetWorkerBaseUrl();
            var metrics = await _metricsService.GetSystemMetricsAsync();

            var registerRequest = new RegistrationRequest(workerBaseUrl, Environment.MachineName, metrics, DateTime.UtcNow);

            var response = await _httpClient.PostAsJsonAsync($"{coreServiceUrl}/api/worker-nodes/register", registerRequest);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var registerResponse = JsonSerializer.Deserialize<RegistrationResponse>(responseContent, JsonOptions);

                if (registerResponse?.Success == true && registerResponse.NodeId.HasValue)
                {
                    _nodeId = registerResponse.NodeId.Value;
                    IsRegistered = true;
                    _logger.LogInformation("节点注册成功，节点ID: {NodeId}", _nodeId);
                }
                else
                {
                    _logger.LogWarning("节点注册失败: {Message}", registerResponse?.ErrorMessage ?? "未知错误");
                }
            }
            else
            {
                _logger.LogWarning("节点注册请求失败，状态码: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "注册节点时发生错误");
        }
    }

    private async Task<bool> SendHeartbeatAsync()
    {
        if (!_nodeId.HasValue)
        {
            _logger.LogWarning("节点ID为空，无法发送心跳");
            return false;
        }

        try
        {
            var coreServiceUrl = GetCoreServiceUrl();
            var metrics = await _metricsService.GetSystemMetricsAsync();

            var heartbeatRequest = new NodeHeartbeat(_nodeId.Value, DateTime.UtcNow, metrics, "healthy");

            var response = await _httpClient.PostAsJsonAsync($"{coreServiceUrl}/api/worker-nodes/{_nodeId.Value}/heartbeat", heartbeatRequest);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogDebug("心跳发送成功");
                return true;
            }

            _logger.LogWarning("心跳发送失败，状态码: {StatusCode}", response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送心跳时发生错误");
            return false;
        }
    }

    private async Task UnregisterNodeAsync()
    {
        if (!_nodeId.HasValue)
            return;

        try
        {
            var coreServiceUrl = GetCoreServiceUrl();
            var response = await _httpClient.DeleteAsync($"{coreServiceUrl}/api/worker-nodes/{_nodeId}");

            if (response.IsSuccessStatusCode)
                _logger.LogInformation("节点注销成功");
            else
                _logger.LogWarning("节点注销失败，状态码: {StatusCode}", response.StatusCode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "注销节点时发生错误");
        }
    }

    private string GetCoreServiceUrl()
    {
        return _configuration["CoreService:BaseUrl"] ?? "http://localhost:5000";
    }

    private string GetWorkerBaseUrl()
    {
        return _configuration["Worker:BaseUrl"] ?? "http://localhost:5127";
    }

    private static TimeSpan CalculateBackoffDelay(int retryCount)
    {
        var baseDelay = TimeSpan.FromSeconds(30);
        var exponentialDelay = TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(2, retryCount - 1));
        var maxDelay = TimeSpan.FromMinutes(5);

        return exponentialDelay > maxDelay ? maxDelay : exponentialDelay;
    }
}
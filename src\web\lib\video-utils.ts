import type {AudioFormat, VideoFormat, YouTubeVideoInfo} from './types';

export function formatDuration(seconds: number): string {
    if (!seconds || seconds <= 0) return '00:00';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

export function formatFileSize(bytes: number): string {
    if (!bytes || bytes <= 0) return '未知大小';

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
}

export function formatViewCount(count: number): string {
    if (!count || count <= 0) return '0';

    if (count >= 1000000000) {
        return `${(count / 1000000000).toFixed(1)}B`;
    }
    if (count >= 1000000) {
        return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
        return `${(count / 1000).toFixed(1)}K`;
    }

    return count.toLocaleString();
}

export function formatPublishedDate(dateString: string): string {
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

        if (diffInSeconds < 60) {
            return '刚刚';
        }

        const diffInMinutes = Math.floor(diffInSeconds / 60);
        if (diffInMinutes < 60) {
            return `${diffInMinutes}分钟前`;
        }

        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) {
            return `${diffInHours}小时前`;
        }

        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays < 30) {
            return `${diffInDays}天前`;
        }

        const diffInMonths = Math.floor(diffInDays / 30);
        if (diffInMonths < 12) {
            return `${diffInMonths}个月前`;
        }

        const diffInYears = Math.floor(diffInMonths / 12);
        return `${diffInYears}年前`;
    } catch {
        return dateString;
    }
}

export function getBestThumbnail(thumbnails?: YouTubeVideoInfo['thumbnails']): string {
    if (!thumbnails) return '';

    return thumbnails.maxres ||
        thumbnails.high ||
        thumbnails.medium ||
        thumbnails.default ||
        '';
}

export function sortVideoFormats(formats: VideoFormat[]): VideoFormat[] {
    return [...formats].sort((a, b) => {
        // 优先显示有音频的格式
        if (a.hasAudio && !b.hasAudio) return -1;
        if (!a.hasAudio && b.hasAudio) return 1;

        // 按分辨率排序（高到低）
        const aHeight = a.height || 0;
        const bHeight = b.height || 0;
        if (aHeight !== bHeight) return bHeight - aHeight;

        // 按帧率排序（高到低）
        const aFps = a.fps || 0;
        const bFps = b.fps || 0;
        if (aFps !== bFps) return bFps - aFps;

        // 按文件大小排序（小到大）
        const aSize = a.filesize || a.filesizeApprox || 0;
        const bSize = b.filesize || b.filesizeApprox || 0;
        return aSize - bSize;
    });
}

export function sortAudioFormats(formats: AudioFormat[]): AudioFormat[] {
    return [...formats].sort((a, b) => {
        // 按比特率排序（高到低）
        const aAbr = a.abr || 0;
        const bAbr = b.abr || 0;
        if (aAbr !== bAbr) return bAbr - aAbr;

        // 按文件大小排序（小到大）
        const aSize = a.filesize || a.filesizeApprox || 0;
        const bSize = b.filesize || b.filesizeApprox || 0;
        return aSize - bSize;
    });
}

export function getVideoQualityLabel(format: VideoFormat): string {
    if (format.resolution) return format.resolution;
    if (format.height) return `${format.height}p`;
    return format.quality || '未知';
}

export function getAudioQualityLabel(format: AudioFormat): string {
    if (format.abr) return `${format.abr}kbps`;
    return format.quality || '未知';
}

export function isHighQuality(format: VideoFormat): boolean {
    const height = format.height || 0;
    return height >= 1080;
}

export function isPremiumQuality(format: VideoFormat | AudioFormat): boolean {
    if ('height' in format) {
        // 视频格式
        const height = format.height || 0;
        const fps = format.fps || 0;
        return height > 1080 || (height === 1080 && fps > 30);
    } else {
        // 音频格式
        const abr = format.abr || 0;
        return abr > 192;
    }
}

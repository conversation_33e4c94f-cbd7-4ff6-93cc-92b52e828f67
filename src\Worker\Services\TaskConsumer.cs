using System.Security.Cryptography;
using System.Text;
using MassTransit;
using Shared.Common;
using Shared.DTOs;
using Shared.Messages;
using Worker.Scheduled;

namespace Worker.Services;

/// <summary>
///     任务消费者
/// </summary>
public class TaskConsumer : IConsumer<ITaskMessage>
{
    private readonly IConfiguration _configuration;
    private readonly HeartbeatService _heartbeatService;
    private readonly ILogger<TaskConsumer> _logger;
    private readonly MetricsService _metricsService;
    private readonly string _nodeId;
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly TaskProcessorService _taskProcessor;

    public TaskConsumer(ILogger<TaskConsumer> logger, TaskProcessorService taskProcessor, IPublishEndpoint publishEndpoint, IConfiguration configuration,
        MetricsService metricsService, HeartbeatService heartbeatService)
    {
        _logger = logger;
        _taskProcessor = taskProcessor;
        _publishEndpoint = publishEndpoint;
        _configuration = configuration;
        _metricsService = metricsService;
        _heartbeatService = heartbeatService;
        _nodeId = Environment.MachineName;
    }

    public async Task Consume(ConsumeContext<ITaskMessage> context)
    {
        var message = context.Message;
        var startTime = DateTime.UtcNow;

        _logger.LogInformation("任务开始: {TaskId}, 类型: {TaskType}, 视频ID: {VideoId}, 质量: {Quality}", message.TaskId, message.TaskType.ToString(),
            message.VideoId ?? "", message.Quality);

        // 记录任务开始
        _metricsService.RecordTask(WorkerTaskStatus.Processing);

        try
        {
            // 发送开始处理消息
            await PublishTaskResultAsync(message.TaskId, WorkerTaskStatus.Processing, 0, null, null, null);

            // 创建进度报告器
            var progress = new Progress<TaskProgressInfo>(async progressInfo =>
            {
                await PublishTaskProgressAsync(message.TaskId, progressInfo.Progress, progressInfo.CurrentStep);
            });

            // 根据任务类型处理
            var result = message.TaskType switch
            {
                WorkerTaskType.VideoDownload => await ProcessVideoDownloadTask(message, progress, context.CancellationToken),
                WorkerTaskType.AudioConvert => await ProcessAudioConvertTask(message, progress, context.CancellationToken),
                WorkerTaskType.GifCreate => await ProcessGifCreateTask(message, progress, context.CancellationToken),
                WorkerTaskType.SubtitleDownload => await ProcessSubtitleDownloadTask(message, progress, context.CancellationToken),
                WorkerTaskType.ThumbnailDownload => await ProcessThumbnailDownloadTask(message, progress, context.CancellationToken),
                WorkerTaskType.CommentDownload => await ProcessCommentDownloadTask(message, progress, context.CancellationToken),
                WorkerTaskType.DescriptionDownload => await ProcessDescriptionDownloadTask(message, progress, context.CancellationToken),
                WorkerTaskType.RingtoneCreate => await ProcessRingtoneCreateTask(message, progress, context.CancellationToken),
                _ => ServiceResult<TaskProcessResult>.Failure($"不支持的任务类型: {message.TaskType}", ErrorCodes.UNSUPPORTED_TASK_TYPE)
            };

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;

            // 发送最终结果
            if (result.IsSuccess)
            {
                await PublishTaskResultAsync(message.TaskId, WorkerTaskStatus.Completed, 100, null, result.Data!.FilePath, result.Data.FileSize);

                // 记录任务完成
                _metricsService.RecordTask(WorkerTaskStatus.Completed);
                _logger.LogInformation("任务完成: {TaskId}, 类型: {TaskType}, 耗时: {Duration:F2}秒, 文件大小: {FileSize}字节, 文件路径: {FilePath}", message.TaskId,
                    message.TaskType.ToString(), duration, result.Data.FileSize, result.Data.FilePath);
            }
            else
            {
                await PublishTaskResultAsync(message.TaskId, WorkerTaskStatus.Failed, 0, result.ErrorMessage, null, null);

                // 记录任务失败
                _metricsService.RecordTask(WorkerTaskStatus.Failed);
                _logger.LogError("任务失败: {TaskId}, 类型: {TaskType}, 错误: {ErrorMessage}, 错误代码: {ErrorCode}, 耗时: {Duration:F2}秒", message.TaskId,
                    message.TaskType.ToString(), result.ErrorMessage ?? "未知错误", result.ErrorCode, duration);
            }
        }
        catch (OperationCanceledException)
        {
            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            _logger.LogInformation("任务已取消: {TaskId}，耗时 {Duration:F2} 秒", message.TaskId, duration);

            _metricsService.RecordTask(WorkerTaskStatus.Failed);
            await PublishTaskResultAsync(message.TaskId, WorkerTaskStatus.Cancelled, 0, "任务已取消", null, null);
        }
        catch (Exception ex)
        {
            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            _logger.LogError(ex, "处理任务时发生意外错误: {TaskId}，耗时 {Duration:F2} 秒", message.TaskId, duration);

            _metricsService.RecordTask(WorkerTaskStatus.Failed);
            await PublishTaskResultAsync(message.TaskId, WorkerTaskStatus.Failed, 0, $"处理任务时发生意外错误: {ex.Message}", null, null);
        }
    }

    /// <summary>
    ///     获取节点ID
    /// </summary>
    private Guid? GetNodeId()
    {
        // 优先使用NodeLifecycleService中的注册节点ID
        var registeredNodeId = _heartbeatService.GetNodeId();
        if (registeredNodeId.HasValue) return registeredNodeId.Value;

        // 如果节点尚未注册，生成一个基于机器名的确定性ID
        // 这确保在节点注册前也能有一个一致的ID
        var machineNameBytes = Encoding.UTF8.GetBytes(_nodeId);
        var hash = SHA256.HashData(machineNameBytes);

        // 取前16字节构造GUID
        var guidBytes = new byte[16];
        Array.Copy(hash, guidBytes, 16);
        return new Guid(guidBytes);
    }

    /// <summary>
    ///     处理视频下载任务
    /// </summary>
    private async Task<ServiceResult<TaskProcessResult>> ProcessVideoDownloadTask(ITaskMessage message, IProgress<TaskProgressInfo> progress,
        CancellationToken cancellationToken)
    {
        // 获取代理（如果可用）
        ProxyInfo? proxy = null;
        try
        {
            // 这里可以通过HTTP客户端调用核心服务的代理API
            // 暂时使用null，表示直连
            proxy = null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取代理失败，使用直连");
        }

        var request = new TaskProcessRequest(message.TaskId, message.VideoId!, message.OutputFormat, message.Quality, message.StartTime, message.EndTime,
            proxy);

        return await _taskProcessor.ProcessVideoDownloadAsync(request, progress, cancellationToken);
    }

    /// <summary>
    ///     处理音频转换任务
    /// </summary>
    private async Task<ServiceResult<TaskProcessResult>> ProcessAudioConvertTask(ITaskMessage message, IProgress<TaskProgressInfo> progress,
        CancellationToken cancellationToken)
    {
        var request = new TaskProcessRequest(message.TaskId, message.VideoId!, message.OutputFormat, message.Quality, message.StartTime, message.EndTime,
            null // TODO: 从代理池获取
        );

        return await _taskProcessor.ProcessAudioConvertAsync(request, progress, cancellationToken);
    }

    /// <summary>
    ///     处理GIF创建任务
    /// </summary>
    private async Task<ServiceResult<TaskProcessResult>> ProcessGifCreateTask(ITaskMessage message, IProgress<TaskProgressInfo> progress,
        CancellationToken cancellationToken)
    {
        var request = new TaskProcessRequest(message.TaskId, message.VideoId!, message.OutputFormat, message.Quality, message.StartTime, message.EndTime,
            null // 代理信息需要从其他地方获取
        );

        return await _taskProcessor.ProcessGifCreateAsync(request, progress, cancellationToken);
    }

    /// <summary>
    ///     处理字幕下载任务
    /// </summary>
    private async Task<ServiceResult<TaskProcessResult>> ProcessSubtitleDownloadTask(ITaskMessage message, IProgress<TaskProgressInfo> progress,
        CancellationToken cancellationToken)
    {
        var request = new TaskProcessRequest(message.TaskId, message.VideoId!, message.OutputFormat, message.Quality, message.StartTime, message.EndTime,
            null // 代理信息需要从其他地方获取
        );

        return await _taskProcessor.ProcessSubtitleDownloadAsync(request, progress, cancellationToken);
    }

    /// <summary>
    ///     处理缩略图下载任务
    /// </summary>
    private async Task<ServiceResult<TaskProcessResult>> ProcessThumbnailDownloadTask(ITaskMessage message, IProgress<TaskProgressInfo> progress,
        CancellationToken cancellationToken)
    {
        var request = new TaskProcessRequest(message.TaskId, message.VideoId!, message.OutputFormat, message.Quality, message.StartTime, message.EndTime,
            null // TODO: 从代理池获取
        );

        return await _taskProcessor.ProcessThumbnailDownloadAsync(request, progress, cancellationToken);
    }

    /// <summary>
    ///     发布任务结果
    /// </summary>
    private async Task PublishTaskResultAsync(Guid taskId, WorkerTaskStatus status, int progress, string? errorMessage, string? outputFilePath, long? fileSize)
    {
        try
        {
            var resultMessage = new TaskResultMessage(taskId, status, progress, errorMessage, outputFilePath, fileSize, null, // ProcessingTime
                GetNodeId(), // 获取正确的节点ID
                DateTime.UtcNow);

            await _publishEndpoint.Publish<ITaskResultMessage>(resultMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布任务结果失败: {TaskId}", taskId);
        }
    }

    /// <summary>
    ///     发布任务进度
    /// </summary>
    private async Task PublishTaskProgressAsync(Guid taskId, int progress, string currentStep)
    {
        try
        {
            var progressMessage = new TaskProgressMessage(taskId, progress, currentStep, null, // EstimatedTimeRemaining
                GetNodeId(), DateTime.UtcNow);

            await _publishEndpoint.Publish<ITaskProgressMessage>(progressMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布任务进度失败: {TaskId}", taskId);
        }
    }

    /// <summary>
    ///     处理评论下载任务
    /// </summary>
    private async Task<ServiceResult<TaskProcessResult>> ProcessCommentDownloadTask(ITaskMessage message, IProgress<TaskProgressInfo> progress,
        CancellationToken cancellationToken)
    {
        var request = new TaskProcessRequest(message.TaskId, message.VideoId!, message.OutputFormat, message.Quality, message.StartTime, message.EndTime,
            null // 代理信息需要从其他地方获取
        );

        return await _taskProcessor.ProcessCommentDownloadAsync(request, progress, cancellationToken);
    }

    /// <summary>
    ///     处理描述下载任务
    /// </summary>
    private async Task<ServiceResult<TaskProcessResult>> ProcessDescriptionDownloadTask(ITaskMessage message, IProgress<TaskProgressInfo> progress,
        CancellationToken cancellationToken)
    {
        var request = new TaskProcessRequest(message.TaskId, message.VideoId!, message.OutputFormat, message.Quality, message.StartTime, message.EndTime,
            null // 代理信息需要从其他地方获取
        );

        return await _taskProcessor.ProcessDescriptionDownloadAsync(request, progress, cancellationToken);
    }

    /// <summary>
    ///     处理铃声制作任务
    /// </summary>
    private async Task<ServiceResult<TaskProcessResult>> ProcessRingtoneCreateTask(ITaskMessage message, IProgress<TaskProgressInfo> progress,
        CancellationToken cancellationToken)
    {
        var request = new TaskProcessRequest(message.TaskId, message.VideoId!, message.OutputFormat, message.Quality, message.StartTime, message.EndTime,
            null // 代理信息需要从其他地方获取
        );

        return await _taskProcessor.ProcessRingtoneCreateAsync(request, progress, cancellationToken);
    }
}
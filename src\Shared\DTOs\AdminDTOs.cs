using Shared.Common;

namespace Shared.DTOs;

public record AdminLoginRequest(string Email, string Password);

public record UpdateUserPlanRequest(UserPlanType PlanType, DateTime? PlanExpiresAt);

public record AdminUserListItem(
    Guid Id,
    UserType UserType,
    string? Email,
    UserPlanType PlanType,
    DateTime? PlanExpiresAt,
    UserAccountStatus Status,
    bool EmailVerified,
    DateTime? LastLoginAt,
    string? LastLoginIp,
    DateTime CreatedAt,
    DateTime LastActiveAt);

public record AdminUserListResponse(
    List<AdminUserListItem> Users,
    int TotalCount,
    int Page,
    int PageSize);

public record AdminUserDetailResponse(
    Guid Id,
    UserType UserType,
    string? Email,
    UserPlanType PlanType,
    DateTime? PlanExpiresAt,
    UserAccountStatus Status,
    bool EmailVerified,
    string? EmailVerificationToken,
    DateTime? EmailVerificationTokenExpiresAt,
    string? PasswordResetToken,
    DateTime? PasswordResetTokenExpiresAt,
    int SecurityVersion,
    int LoginFailureCount,
    DateTime? AccountLockedUntil,
    DateTime? LastLoginAt,
    string? LastLoginIp,
    DateTime CreatedAt,
    DateTime LastActiveAt,
    DateTime UpdatedAt,
    AdminUserStatsResponse Stats);

public record AdminUserStatsResponse(
    int TotalTasks,
    int CompletedTasks,
    int FailedTasks,
    int ActiveSessions,
    long TotalDownloadSize,
    DateTime? LastTaskAt);

public record AdminUserSessionResponse(
    Guid Id,
    Guid UserId,
    string? IpAddress,
    string? UserAgent,
    DateTime CreatedAt,
    DateTime ExpiresAt,
    bool IsExpired);

public record AdminUserSessionListResponse(
    List<AdminUserSessionResponse> Sessions,
    int TotalCount,
    int Page,
    int PageSize);

public record AdminUserTaskListResponse(
    List<WorkerTaskResponse> WorkerTasks,
    List<BatchTaskResponse> BatchTasks,
    int TotalCount,
    int Page,
    int PageSize);

public record AdminUserContentResponse(
    string VideoId,
    string? VideoTitle,
    string? VideoUrl,
    string? ThumbnailUrl,
    int TaskCount,
    DateTime FirstTaskAt,
    DateTime LastTaskAt);

public record AdminUserContentListResponse(
    List<AdminUserContentResponse> Content,
    int TotalCount,
    int Page,
    int PageSize);

public record AdminUserBillingResponse(
    Guid UserId,
    UserPlanType CurrentPlan,
    DateTime? CurrentPlanExpiresAt,
    List<AdminBillingHistoryResponse> History);

public record AdminBillingHistoryResponse(
    Guid Id,
    UserPlanType PlanType,
    DateTime StartDate,
    DateTime? EndDate,
    decimal Amount,
    string Currency,
    string Status,
    DateTime CreatedAt);

public record AdminUserListRequest(
    int Page = 1,
    int PageSize = 20,
    string? Search = null,
    UserType? UserType = null,
    UserAccountStatus? Status = null);

public record ResetPasswordResponse(
    string NewPassword,
    DateTime ResetAt);
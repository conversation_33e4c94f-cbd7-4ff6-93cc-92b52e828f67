using Shared.Common;

namespace Shared.DTOs;

public record AdminLoginRequest(string Email, string Password);

public record UpdateUserPlanRequest(UserPlanType PlanType, DateTime? PlanExpiresAt);

public record UserListItem(
    Guid Id,
    UserType UserType,
    string? Email,
    UserPlanType PlanType,
    DateTime? PlanExpiresAt,
    UserAccountStatus Status,
    bool EmailVerified,
    DateTime? LastLoginAt,
    string? LastLoginIp,
    DateTime CreatedAt,
    DateTime LastActiveAt);

public record UserListResponse(
    List<UserListItem> Users,
    int TotalCount,
    int Page,
    int PageSize);

public record UserDetailResponse(
    Guid Id,
    UserType UserType,
    string? Email,
    UserPlanType PlanType,
    DateTime? PlanExpiresAt,
    UserAccountStatus Status,
    bool EmailVerified,
    string? EmailVerificationToken,
    DateTime? EmailVerificationTokenExpiresAt,
    string? PasswordResetToken,
    DateTime? PasswordResetTokenExpiresAt,
    int SecurityVersion,
    int LoginFailureCount,
    DateTime? AccountLockedUntil,
    DateTime? LastLoginAt,
    string? LastLoginIp,
    DateTime CreatedAt,
    DateTime LastActiveAt,
    DateTime UpdatedAt,
    UserStatsResponse Stats);

public record UserStatsResponse(
    int TotalTasks,
    int CompletedTasks,
    int FailedTasks,
    int ActiveSessions,
    long TotalDownloadSize,
    DateTime? LastTaskAt);

public record UserSessionResponse(
    Guid Id,
    Guid UserId,
    string? IpAddress,
    string? UserAgent,
    DateTime CreatedAt,
    DateTime ExpiresAt,
    bool IsExpired);

public record UserSessionListResponse(
    List<UserSessionResponse> Sessions,
    int TotalCount,
    int Page,
    int PageSize);

public record UserTaskListResponse(
    List<WorkerTaskResponse> WorkerTasks,
    List<BatchTaskResponse> BatchTasks,
    int TotalCount,
    int Page,
    int PageSize);

public record UserContentResponse(
    string VideoId,
    string? VideoTitle,
    string? VideoUrl,
    string? ThumbnailUrl,
    int TaskCount,
    DateTime FirstTaskAt,
    DateTime LastTaskAt);

public record UserContentListResponse(
    List<UserContentResponse> Content,
    int TotalCount,
    int Page,
    int PageSize);

public record UserBillingResponse(
    Guid UserId,
    UserPlanType CurrentPlan,
    DateTime? CurrentPlanExpiresAt,
    List<BillingHistoryResponse> History);

public record BillingHistoryResponse(
    Guid Id,
    UserPlanType PlanType,
    DateTime StartDate,
    DateTime? EndDate,
    decimal Amount,
    string Currency,
    string Status,
    DateTime CreatedAt);

public record UserListRequest(
    int Page = 1,
    int PageSize = 20,
    string? Search = null,
    UserType? UserType = null,
    UserAccountStatus? Status = null);

public record ResetPasswordResponse(
    string NewPassword,
    DateTime ResetAt);
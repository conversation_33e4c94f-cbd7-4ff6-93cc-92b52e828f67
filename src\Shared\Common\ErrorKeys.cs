namespace Shared.Common;

public static class ErrorKeys
{
    public const string VALIDATION_FAILED = "VALIDATION_FAILED";
    public const string INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR";

    public const string EMAIL_ALREADY_REGISTERED = "EMAIL_ALREADY_REGISTERED";
    public const string INVALID_CREDENTIALS = "INVALID_CREDENTIALS";
    public const string ACCOUNT_STATUS_INACTIVE = "ACCOUNT_STATUS_INACTIVE";
    public const string ACCOUNT_LOCKED = "ACCOUNT_LOCKED";
    public const string ACCOUNT_LOCKED_TOO_MANY_ATTEMPTS = "ACCOUNT_LOCKED_TOO_MANY_ATTEMPTS";
    public const string USER_NOT_FOUND_OR_NO_PASSWORD = "USER_NOT_FOUND_OR_NO_PASSWORD";
    public const string CURRENT_PASSWORD_MISMATCH = "CURRENT_PASSWORD_MISMATCH";
    public const string USER_NOT_FOUND = "USER_NOT_FOUND";
    public const string EMAIL_VERIFICATION_TOKEN_INVALID_OR_EXPIRED = "EMAIL_VERIFICATION_TOKEN_INVALID_OR_EXPIRED";
    public const string EMAIL_ALREADY_VERIFIED = "EMAIL_ALREADY_VERIFIED";

    public const string VALIDATION_NOT_EMPTY = "VALIDATION_NOT_EMPTY";
    public const string VALIDATION_EMAIL_INVALID = "VALIDATION_EMAIL_INVALID";
    public const string VALIDATION_MAX_LENGTH = "VALIDATION_MAX_LENGTH";
    public const string VALIDATION_MIN_LENGTH = "VALIDATION_MIN_LENGTH";
    public const string VALIDATION_PASSWORD_COMPLEXITY = "VALIDATION_PASSWORD_COMPLEXITY";
    public const string VALIDATION_NOT_EQUAL = "VALIDATION_NOT_EQUAL";

    public const string NOT_FOUND = "NOT_FOUND";
    public const string CONFLICT = "CONFLICT";
}
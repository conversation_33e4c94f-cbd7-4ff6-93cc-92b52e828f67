import {Metadata} from 'next'
import {Header} from '@/components/layout/Header'
import {Footer} from '@/components/layout/Footer'

export const metadata: Metadata = {
    title: 'Privacy Policy | YouTube Downloader',
    description: 'Privacy Policy for YouTube Downloader - Learn how we collect, use, and protect your personal information.',
}

export default function PrivacyPage() {
    return (
        <div className="min-h-screen">
            <Header/>

            <div className="container mx-auto px-4 py-8 max-w-4xl">
                <div className="space-y-8">
                    <div className="text-center space-y-4">
                        <h1 className="text-4xl font-bold">隐私政策</h1>
                        <p className="text-muted-foreground">
                            最后更新时间：{new Date().toLocaleDateString('zh-CN')}
                        </p>
                    </div>

                    <div className="prose max-w-none space-y-8">
                        <section>
                            <h2 className="text-2xl font-semibold mb-4">概述</h2>
                            <p className="text-muted-foreground">
                                我们重视您的隐私，并致力于保护您的个人信息。本隐私政策说明了我们如何收集、使用、存储和保护您在使用我们的YouTube下载服务时提供的信息。
                            </p>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">信息收集</h2>
                            <p className="text-muted-foreground mb-4">我们可能收集以下信息：</p>
                            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                                <li>您提供的YouTube视频链接</li>
                                <li>设备信息和浏览器类型</li>
                                <li>IP地址和访问日志</li>
                                <li>Cookie和类似技术收集的数据</li>
                            </ul>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">信息使用</h2>
                            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                                <li>提供和维护我们的下载服务</li>
                                <li>处理您的下载请求</li>
                                <li>改进服务质量和用户体验</li>
                                <li>防止滥用和确保服务安全</li>
                            </ul>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">数据安全</h2>
                            <p className="text-muted-foreground mb-4">我们采用以下安全措施：</p>
                            <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                                <li>使用HTTPS加密传输所有数据</li>
                                <li>下载文件24小时后自动删除</li>
                                <li>定期更新和维护安全系统</li>
                            </ul>
                        </section>

                        <section>
                            <h2 className="text-2xl font-semibold mb-4">联系我们</h2>
                            <p className="text-muted-foreground">
                                如果您对本隐私政策有任何疑问，请联系我们：
                                <br/>
                                邮箱：<EMAIL>
                            </p>
                        </section>
                    </div>
                </div>
            </div>

            <Footer/>
        </div>
    )
}

'use client';

import React from 'react';
import {Alert<PERSON>riangle, RefreshCw} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';

interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
}

interface ErrorBoundaryProps {
    children: React.ReactNode;
    fallback?: React.ComponentType<{ error?: Error; retry: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = {hasError: false};
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return {hasError: true, error};
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('<PERSON><PERSON>rBoundary caught an error:', error, errorInfo);
    }

    retry = () => {
        this.setState({hasError: false, error: undefined});
    };

    render() {
        if (this.state.hasError) {
            if (this.props.fallback) {
                const FallbackComponent = this.props.fallback;
                return <FallbackComponent error={this.state.error} retry={this.retry}/>;
            }

            return <DefaultErrorFallback error={this.state.error} retry={this.retry}/>;
        }

        return this.props.children;
    }
}

function DefaultErrorFallback({error, retry}: { error?: Error; retry: () => void }) {
    return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                        <AlertTriangle className="h-6 w-6 text-red-600"/>
                    </div>
                    <CardTitle className="text-lg">出现了一些问题</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                    <p className="text-sm text-muted-foreground">
                        {error?.message || '页面加载时出现错误，请稍后重试。'}
                    </p>
                    <Button onClick={retry} className="w-full">
                        <RefreshCw className="h-4 w-4 mr-2"/>
                        重新加载
                    </Button>
                </CardContent>
            </Card>
        </div>
    );
}

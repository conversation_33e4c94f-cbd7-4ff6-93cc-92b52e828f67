'use client';

import {useState} from 'react';
import Link from 'next/link';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {ExternalLink} from 'lucide-react';

export default function TestDownloadPage() {
    const [videoId, setVideoId] = useState('dQw4w9WgXcQ');

    const testUrls = [
        {id: 'dQw4w9WgXcQ', title: '<PERSON> - Never Gonna Give You Up'},
        {id: 'test123', title: '测试视频ID'},
        {id: 'invalid', title: '无效视频ID'},
        {id: '', title: '空视频ID'},
    ];

    return (
        <div className="min-h-screen bg-background p-8">
            <div className="container mx-auto max-w-4xl">
                <Card>
                    <CardHeader>
                        <CardTitle>下载页面测试工具</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* 自定义视频ID测试 */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">自定义视频ID:</label>
                            <div className="flex gap-2">
                                <Input
                                    value={videoId}
                                    onChange={(e) => setVideoId(e.target.value)}
                                    placeholder="输入YouTube视频ID"
                                />
                                <Button asChild>
                                    <Link href={`/download?videoId=${videoId}`}>
                                        <ExternalLink className="h-4 w-4 mr-2"/>
                                        测试
                                    </Link>
                                </Button>
                            </div>
                        </div>

                        {/* 预设测试用例 */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">预设测试用例:</label>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {testUrls.map((test) => (
                                    <Card key={test.id} className="p-4">
                                        <div className="space-y-2">
                                            <h3 className="font-medium">{test.title}</h3>
                                            <p className="text-sm text-muted-foreground">
                                                ID: {test.id || '(空)'}
                                            </p>
                                            <Button asChild size="sm" className="w-full">
                                                <Link href={`/download?videoId=${test.id}`}>
                                                    <ExternalLink className="h-4 w-4 mr-2"/>
                                                    测试此用例
                                                </Link>
                                            </Button>
                                        </div>
                                    </Card>
                                ))}
                            </div>
                        </div>

                        {/* 返回首页 */}
                        <div className="pt-4 border-t">
                            <Button asChild variant="outline">
                                <Link href="/">返回首页</Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}

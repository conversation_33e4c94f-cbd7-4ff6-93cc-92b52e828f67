"use client"

import * as React from "react";
import {
    Dialog as BaseDialog,
    DialogContent as BaseDialogContent,
    DialogDescription as BaseDialogDescription,
    DialogHeader as BaseDialogHeader,
    DialogOverlay as BaseDialogOverlay,
    DialogTitle as BaseDialogTitle,
    DialogTrigger as BaseDialogTrigger
} from "@/components/ui/dialog";
import {cn} from "@/lib/utils";

const Dialog = BaseDialog;
const DialogTrigger = BaseDialogTrigger;

function DialogContent({
                           className,
                           children,
                           ...props
                       }: React.ComponentProps<typeof BaseDialogContent>) {
    return (
        <>
            <BaseDialogOverlay className="backdrop-blur-[2px] bg-white/20"/>
            <BaseDialogContent
                className={cn(
                    className
                )}
                {...props}
            >
                {children}
            </BaseDialogContent>
        </>
    );
}

const DialogHeader = BaseDialogHeader;
const DialogTitle = BaseDialogTitle;
const DialogDescription = BaseDialogDescription;

export {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
};